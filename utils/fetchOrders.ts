// utils/fetchOrders.ts
import { getAuth } from 'firebase/auth';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';

export const fetchOrders = async () => {
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user) {
        throw new Error('User not authenticated');
    }

    const ordersRef = collection(db, 'users', user.uid, 'orders');
    const ordersSnapshot = await getDocs(ordersRef);
    return ordersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};