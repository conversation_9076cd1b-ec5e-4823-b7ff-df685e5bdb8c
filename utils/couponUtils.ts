import { db } from '@/config/firebaseConfig';
import { collection, query, where, getDocs, doc, getDoc, updateDoc, arrayUnion } from 'firebase/firestore';
import { Coupon, CouponValidationResult } from '@/types/coupon';

export const validateCoupon = async (
    couponCode: string,
    userId: string,
    orderTotal: number,
    isFirstOrder: boolean = false
): Promise<CouponValidationResult> => {
    try {
        console.log('Kupon doğrulama başladı:', { couponCode, userId, orderTotal, isFirstOrder });

        // İlk sipariş kuponlarını kontrol et
        const firstOrderCouponsRef = collection(db, 'firstOrderCoupon');
        const firstOrderQuery = query(firstOrderCouponsRef, where('code', '==', couponCode));
        const firstOrderSnapshot = await getDocs(firstOrderQuery);

        console.log('İlk sipariş kupon sorgusu sonucu:', firstOrderSnapshot.size, 'adet bulundu');

        if (!firstOrderSnapshot.empty) {
            const couponDoc = firstOrderSnapshot.docs[0];
            const couponData = couponDoc.data();
            console.log('İlk sipariş kuponu bulundu:', couponData);

            const coupon = {
                id: couponDoc.id,
                ...couponData,
                type: 'firstOrder' // İlk sipariş kuponları için tip ekle
            } as Coupon;

            // İlk sipariş kuponunu sadece ilk siparişte kullanabilir
            if (!isFirstOrder) {
                return {
                    isValid: false,
                    message: 'Bu kupon sadece ilk siparişinizde kullanılabilir.',
                    discount: 0
                };
            }

            // Kupon aktif mi?
            if (!coupon.isActive) {
                return {
                    isValid: false,
                    message: 'Bu kupon artık geçerli değil.',
                    discount: 0
                };
            }

            // Minimum sipariş tutarı kontrolü
            if (orderTotal < coupon.minOrderAmount) {
                return {
                    isValid: false,
                    message: `Bu kupon minimum ${coupon.minOrderAmount} TL sipariş için geçerlidir.`,
                    discount: 0
                };
            }

            // Kullanıcı daha önce bu kuponu kullanmış mı?
            if (coupon.usedBy && coupon.usedBy.includes(userId)) {
                return {
                    isValid: false,
                    message: 'Bu kuponu daha önce kullandınız.',
                    discount: 0
                };
            }

            return {
                isValid: true,
                message: 'Kupon başarıyla uygulandı!',
                discount: coupon.discountAmount,
                coupon
            };
        }

        // Genel kuponları kontrol et (eğer varsa)
        console.log('Genel kuponları kontrol ediliyor...');
        const generalCouponsRef = collection(db, 'coupons');
        const generalQuery = query(generalCouponsRef, where('code', '==', couponCode));
        const generalSnapshot = await getDocs(generalQuery);

        console.log('Genel kupon sorgusu sonucu:', generalSnapshot.size, 'adet bulundu');

        if (!generalSnapshot.empty) {
            const couponDoc = generalSnapshot.docs[0];
            const coupon = {
                id: couponDoc.id,
                ...couponDoc.data(),
                type: 'general' // Genel kuponlar için tip ekle
            } as Coupon;

            // Kupon aktif mi?
            if (!coupon.isActive) {
                return {
                    isValid: false,
                    message: 'Bu kupon artık geçerli değil.',
                    discount: 0
                };
            }

            // Minimum sipariş tutarı kontrolü
            if (orderTotal < coupon.minOrderAmount) {
                return {
                    isValid: false,
                    message: `Bu kupon minimum ${coupon.minOrderAmount} TL sipariş için geçerlidir.`,
                    discount: 0
                };
            }

            // Kullanıcı daha önce bu kuponu kullanmış mı?
            if (coupon.usedBy && coupon.usedBy.includes(userId)) {
                return {
                    isValid: false,
                    message: 'Bu kuponu daha önce kullandınız.',
                    discount: 0
                };
            }

            // Maksimum kullanım kontrolü
            if (coupon.maxUsage && coupon.currentUsage && coupon.currentUsage >= coupon.maxUsage) {
                return {
                    isValid: false,
                    message: 'Bu kuponun kullanım limiti dolmuştur.',
                    discount: 0
                };
            }

            return {
                isValid: true,
                message: 'Kupon başarıyla uygulandı!',
                discount: coupon.discountAmount,
                coupon
            };
        }

        console.log('Hiçbir kupon bulunamadı');
        return {
            isValid: false,
            message: 'Geçersiz kupon kodu.',
            discount: 0
        };

    } catch (error) {
        console.error('Kupon doğrulama hatası:', error);
        return {
            isValid: false,
            message: 'Kupon doğrulanırken bir hata oluştu.',
            discount: 0
        };
    }
};

export const applyCoupon = async (coupon: Coupon, userId: string): Promise<boolean> => {
    try {
        const collectionName = coupon.type === 'firstOrder' ? 'firstOrderCoupon' : 'coupons';
        const couponRef = doc(db, collectionName, coupon.id);

        await updateDoc(couponRef, {
            usedBy: arrayUnion(userId),
            currentUsage: (coupon.currentUsage || 0) + 1
        });

        return true;
    } catch (error) {
        console.error('Kupon uygulama hatası:', error);
        return false;
    }
};
