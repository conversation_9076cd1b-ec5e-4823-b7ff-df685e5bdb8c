// Firebase hata loglama ve monitoring
export interface FirebaseError {
    code: string;
    message: string;
    timestamp: Date;
    userAgent: string;
    phoneNumber?: string;
}

export const logFirebaseError = (error: any, context: string, phoneNumber?: string) => {
    const errorLog: FirebaseError = {
        code: error.code || 'unknown',
        message: error.message || 'Unknown error',
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        phoneNumber: phoneNumber
    };

    // Console'a detaylı log
    console.error(`Firebase Error in ${context}:`, errorLog);

    // Eğer production'da isek, external logging service'e gönder
    if (process.env.NODE_ENV === 'production') {
        // Burada external logging service'e gönderebilirsiniz
        // Örnek: Sentry, LogRocket, vb.
    }

    return errorLog;
};

export const getErrorMessage = (error: any): string => {
    if (error.code === 'auth/too-many-requests') {
        return 'Çok fazla istek gönderildi. Lütfen 1 saat sonra tekrar deneyin.';
    } else if (error.code === 'auth/quota-exceeded') {
        return 'SMS kotası doldu. Lütfen daha sonra tekrar deneyin.';
    } else if (error.message?.includes('503') || error.message?.includes('Service Unavailable')) {
        return 'SMS servisi geçici olarak kullanılamıyor. Lütfen 10-15 dakika sonra tekrar deneyin.';
    } else if (error.code === 'auth/invalid-phone-number') {
        return 'Geçersiz telefon numarası. Lütfen kontrol edin.';
    } else if (error.code === 'auth/captcha-check-failed') {
        return 'Güvenlik doğrulaması başarısız. Sayfayı yenileyin ve tekrar deneyin.';
    } else {
        return `Hata: ${error.message || 'Bilinmeyen hata'}`;
    }
};
