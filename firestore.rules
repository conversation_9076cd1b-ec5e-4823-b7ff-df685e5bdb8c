rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Allow everyone to read all collections
    match /{document=**} {
      allow read: if true;
    }

    // 'users' collection
    match /users/{userId} {
      // Allow anyone to create a user document
      allow create: if true;
      // Allow authenticated users to read and write their own user document
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // 'orders' collection
    match /orders/{orderId} {
      // Allow read and write access only to authenticated users
      allow read, write: if request.auth != null;
    }

    // Other collections
    match /{document=**} {
      // Allow read access to everyone
      allow read: if true;
      // Allow write access only to authenticated users
      allow write: if request.auth != null;
    }
  }
}