// hooks/useSliders.ts
import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';

interface Slider {
    id: string;
    title: string;
    imageUrl: string;
    altText: string;
    order: number;
}

export const useSliders = () => {
    const [sliders, setSliders] = useState<Slider[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [currentIndex, setCurrentIndex] = useState(0);

    // Slider verilerini çek
    useEffect(() => {
        const fetchSliders = async () => {
            try {
                const slidersRef = collection(db, 'sliders');
                const q = query(slidersRef, orderBy('order', 'asc'));
                const querySnapshot = await getDocs(q);

                const fetchedSliders = querySnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                } as Slider));

                setSliders(fetchedSliders);
            } catch (err) {
                console.error('Slider verilerini çekerken hata:', err);
                setError('Slider verileri yüklenemedi.');
            } finally {
                setLoading(false);
            }
        };

        fetchSliders();
    }, []);

    // Otomatik slider değişimi
    useEffect(() => {
        if (sliders.length === 0) return;

        const interval = setInterval(() => {
            setCurrentIndex(prev => (prev + 1) % sliders.length);
        }, 2500); // 2.5 saniye

        return () => clearInterval(interval);
    }, [sliders.length]);

    return {
        sliders,
        currentSlider: sliders[currentIndex],
        loading,
        error,
        currentIndex
    };
};