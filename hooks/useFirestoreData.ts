// hooks/useFirestoreData.ts
import { useEffect, useState, useMemo, useCallback } from 'react';
import { db } from '../config/firebaseConfig';
import { collection, getDocs, query, where, QuerySnapshot, DocumentData } from 'firebase/firestore';

interface Firm {
    id: string;
    name: string;
    description: string;
    banner: string;
    visible: boolean;
    categories: string[];
}

interface Category {
    id: string;
    name: string;
    imageUrl: string;
}

interface Product {
    id: string;
    name: string;
    description: string;
    imageUrl: string;
    categoryId: string;
    firmId: string;
}

// Cache mekanizması
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 dakika

const getCachedData = (key: string) => {
    const cachedData = cache.get(key);
    if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {
        return cachedData.data;
    }
    return null;
};

const setCachedData = (key: string, data: any) => {
    cache.set(key, { data, timestamp: Date.now() });
};

const desiredCategories = [
    'DÖNER',
    'ETLİEKMEK',
    'SULU YEMEK',
    'KAHVALTI',
    'PİZZA',
    'BURGER',
    'TATLI',
    'KASAP'
];

export function useFirestoreData(categoryId?: string) {
    const [firms, setFirms] = useState<Firm[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [allCategories, setAllCategories] = useState<Category[]>([]);
    const [products, setProducts] = useState<Product[]>([]);
    const [loading, setLoading] = useState(true);

    // Veri çekme işlemini optimize etmek için useCallback kullanımı
    const fetchDataFromFirestore = useCallback(async () => {
        const cacheKey = `firestore-data-${categoryId || 'all'}`;
        const cachedData = getCachedData(cacheKey);

        if (cachedData) {
            setFirms(cachedData.firms);
            setCategories(cachedData.categories);
            setAllCategories(cachedData.allCategories);
            setProducts(cachedData.products);
            setLoading(false);
            return;
        }

        try {
            // Firma sorgusunu oluştur
            const firmsQuery = categoryId
                ? query(collection(db, 'firms'), where('categories', 'array-contains', categoryId))
                : collection(db, 'firms');

            // Tüm sorguları paralel olarak çalıştır
            const [firmsSnapshot, categoriesSnapshot, allCategoriesSnapshot, productsSnapshot]: QuerySnapshot<DocumentData>[] =
                await Promise.all([
                    getDocs(firmsQuery),
                    getDocs(query(collection(db, 'categories'), where('name', 'in', desiredCategories))),
                    getDocs(collection(db, 'categories')),
                    getDocs(collection(db, 'products'))
                ]);

            // Veri dönüşümlerini optimize et
            const transformData = (snapshot: QuerySnapshot<DocumentData>) =>
                snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

            const firmsData = transformData(firmsSnapshot) as Firm[];
            const categoriesData = transformData(categoriesSnapshot) as Category[];
            const allCategoriesData = transformData(allCategoriesSnapshot) as Category[];
            const productsData = transformData(productsSnapshot) as Product[];

            // State'leri güncelle
            setFirms(firmsData);
            setCategories(categoriesData);
            setAllCategories(allCategoriesData);
            setProducts(productsData);

            // Cache'e kaydet
            setCachedData(cacheKey, {
                firms: firmsData,
                categories: categoriesData,
                allCategories: allCategoriesData,
                products: productsData
            });

        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            setLoading(false);
        }
    }, [categoryId]);

    // Kategorileri sıralama işlemini optimize et
    const sortedCategories = useMemo(() =>
            [...categories].sort((a, b) => a.name.localeCompare(b.name)),
        [categories]
    );

    useEffect(() => {
        fetchDataFromFirestore();
    }, [fetchDataFromFirestore]);

    return {
        firms,  // Tüm firmaları döndür, visible filtresi olmadan
        categories: sortedCategories,
        allCategories,
        products,
        loading
    };
}