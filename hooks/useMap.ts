import { useState, useEffect, useRef } from 'react';
import { useJsApiLoader, Libraries } from '@react-google-maps/api';

const mapContainerStyle = {
    width: '100%',
    height: '400px',
};

const center = {
    lat: 37.4192,
    lng: 31.8457
};

const libraries: Libraries = ['places'];

interface Location {
    lat: number;
    lng: number;
}

export const useMap = () => {
    const [location, setLocation] = useState<Location>(center);
    const mapRef = useRef<google.maps.Map | null>(null);
    const markerRef = useRef<google.maps.Marker | null>(null);

    const { isLoaded, loadError } = useJsApiLoader({
        googleMapsApiKey: 'AIzaSyDPkqccLCgI9uwbLod0vQjR99hmIn2aKWI',
        libraries,
    });

    useEffect(() => {
        if (isLoaded && mapRef.current) {
            if (markerRef.current) {
                markerRef.current.setPosition(location);
                markerRef.current.setTitle("Adresiniz");
            } else {
                markerRef.current = new google.maps.Marker({
                    map: mapRef.current,
                    position: location,
                    title: "Adresiniz",
                });
            }
        }
    }, [isLoaded, location]);

    const handleMapClick = (e: google.maps.MapMouseEvent) => {
        if (e.latLng) {
            setLocation({
                lat: e.latLng.lat(),
                lng: e.latLng.lng()
            });
        }
    };

    return { isLoaded, loadError, location, setLocation, mapContainerStyle, handleMapClick, mapRef };
};

