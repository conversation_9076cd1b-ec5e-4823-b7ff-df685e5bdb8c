import { useState, useEffect } from 'react';
import { db } from '@/config/firebaseConfig';
import { collection, getDocs } from 'firebase/firestore';

interface RatingData {
    rating: number;
    reviewCount: number;
}

interface Review {
    rating: number;
    comment: string;
    createdAt: any;
    userId: string;
}

export const useFirmRating = (firmId: string) => {
    const [ratingData, setRatingData] = useState<RatingData | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchRating = async () => {
            if (!firmId) {
                setLoading(false);
                return;
            }

            try {
                // reviews koleksiyonu firmsId altında bir subkoleksiyon
                const reviewsRef = collection(db, 'firms', firmId, 'reviews');
                const querySnapshot = await getDocs(reviewsRef);

                if (!querySnapshot.empty) {
                    let totalRating = 0;
                    let validReviews = 0;

                    querySnapshot.forEach((doc) => {
                        const review = doc.data() as Review;
                        if (typeof review.rating === 'number' && !isNaN(review.rating)) {
                            totalRating += review.rating;
                            validReviews++;
                        }
                    });

                    if (validReviews > 0) {
                        setRatingData({
                            rating: Number((totalRating / validReviews).toFixed(1)),
                            reviewCount: validReviews
                        });
                    } else {
                        setRatingData(null);
                    }
                } else {
                    setRatingData(null);
                }
            } catch (err) {
                console.error('Error fetching ratings:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchRating();
    }, [firmId]);

    return { ratingData, loading };
};