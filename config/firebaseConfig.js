import { initializeApp } from "firebase/app";
import { getAuth, setPersistence, browserLocalPersistence } from "firebase/auth";
import { initializeFirestore, Timestamp } from "firebase/firestore";
import { getStorage } from "firebase/storage";

const firebaseConfig = {
    apiKey: "AIzaSyDPkqccLCgI9uwbLod0vQjR99hmIn2aKWI",
    authDomain: "yemekkapimda-46ecc.firebaseapp.com",
    projectId: "yemekkapimda-46ecc",
    storageBucket: "yemekkapimda-46ecc.appspot.com",
    messagingSenderId: "201585951998",
    appId: "1:201585951998:web:ddd2de9927fb628b1211af",
    measurementId: "G-H8SR5F9XGF"
};

const app = initializeApp(firebaseConfig);
const db = initializeFirestore(app, {
    experimentalAutoDetectLongPolling: true,
});
const auth = getAuth(app);
setPersistence(auth, browserLocalPersistence).catch((error) => {
    console.error("Error setting persistence:", error);
});

const storage = getStorage(app);

let analytics;
if (typeof window !== "undefined") {
    import("firebase/analytics").then(({ getAnalytics }) => {
        analytics = getAnalytics(app);
    });
}

export { app, analytics, auth, db, storage, Timestamp };