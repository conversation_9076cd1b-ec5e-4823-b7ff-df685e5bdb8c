'use client';

import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';

// Firestore'daki veri yapı<PERSON><PERSON>na uygun interface
interface Slider {
    id: string;
    imageUrl: string;
    altText: string;
    title: string;
}

// Fallback images
const fallbackImages = [
    "/banner1.jpeg",
    "/banner2.jpeg",
    "/banner3.jpeg"
];

export default function Banner() {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [sliders, setSliders] = useState<Slider[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchSliders = async () => {
            try {
                const slidersRef = collection(db, 'sliders');
                const querySnapshot = await getDocs(slidersRef);

                if (!querySnapshot.empty) {
                    const slidersData = querySnapshot.docs.map(doc => ({
                        id: doc.id,
                        imageUrl: doc.data().imageUrl || '',
                        altText: doc.data().altText || '',
                        title: doc.data().title || ''
                    }));
                    setSliders(slidersData);
                } else {
                    // Firestore'da veri yoksa fallback verilerini kullan
                    const fallbackData = fallbackImages.map((image, index) => ({
                        id: `fallback-${index}`,
                        imageUrl: image,
                        altText: `Slider ${index + 1}`,
                        title: `Slider ${index + 1}`
                    }));
                    setSliders(fallbackData);
                }
            } catch (error) {
                console.error('Slider verilerini çekerken hata:', error);
                // Hata durumunda fallback verilerini kullan
                const fallbackData = fallbackImages.map((image, index) => ({
                    id: `fallback-${index}`,
                    imageUrl: image,
                    altText: `Slider ${index + 1}`,
                    title: `Slider ${index + 1}`
                }));
                setSliders(fallbackData);
            } finally {
                setLoading(false);
            }
        };

        fetchSliders();
    }, []);

    // Otomatik slider değişimi
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentIndex(prevIndex => (prevIndex + 1) % sliders.length);
        }, 2500);

        return () => clearInterval(interval);
    }, [sliders.length]);

    if (loading) {
        return (
            <div className="relative w-full h-[40vh] bg-gray-200 animate-pulse">
                <div className="absolute inset-0 flex items-center justify-center">
                    <span className="sr-only">Yükleniyor...</span>
                </div>
            </div>
        );
    }

    return (
        <div className="relative w-full h-[40vh] overflow-hidden">
            <Image
                src={sliders[currentIndex]?.imageUrl || fallbackImages[0]}
                alt={sliders[currentIndex]?.altText || `Slider ${currentIndex + 1}`}
                fill
                className="object-cover"
                priority
                sizes="100vw"
            />
            {/* Slider noktaları */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                {sliders.map((_, index) => (
                    <button
                        key={index}
                        onClick={() => setCurrentIndex(index)}
                        className={`w-2 h-2 rounded-full transition-colors duration-200 
                            ${index === currentIndex ? 'bg-white' : 'bg-white/50'}`}
                        aria-label={`Go to slide ${index + 1}`}
                    />
                ))}
            </div>
        </div>
    );
}