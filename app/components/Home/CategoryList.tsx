'use client';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import {FaSpinner} from 'react-icons/fa';

interface Category {
    id: string;
    name: string;
    imageUrl: string;
    productCount?: number;
}

interface CategoryListProps {
    categories: Category[];
}

const CategoryList: React.FC<CategoryListProps> = ({categories}) => (
    <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
            {/* Başlık */}
            <div className="text-center max-w-2xl mx-auto mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                    Kategoriler
                </h2>
                <p className="text-gray-600 mb-6">
                    Dilediğiniz kategoriden sipariş verebilirsiniz
                </p>
                <div className="w-24 h-1.5 bg-orange-600 mx-auto rounded-full"/>
            </div>

            {/* <PERSON><PERSON><PERSON> */}
            {categories.length > 0 ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-6">
                    {categories.map((category) => (
                        <Link
                            key={category.id}
                            href={`/categories/${category.id}`}
                            className="group relative bg-white rounded-2xl p-4 transition-all duration-300
                                     hover:shadow-xl hover:-translate-y-1 overflow-hidden"
                        >
                            {/* Hover Background Effect */}
                            <div className="absolute inset-0 bg-gradient-to-b from-orange-500/0 to-orange-500/10
                                          opacity-0 group-hover:opacity-100 transition-opacity duration-300"/>

                            {/* Content Container */}
                            <div className="relative flex flex-col items-center">
                                {/* Image Container */}
                                <div className="relative w-24 h-24 mb-4">
                                    <div className="absolute inset-0 bg-orange-100 rounded-2xl transform
                                                  rotate-6 group-hover:rotate-12 transition-transform duration-300"/>
                                    <div className="absolute inset-0 bg-orange-50 rounded-2xl transform
                                                  -rotate-6 group-hover:-rotate-12 transition-transform duration-300"/>
                                    <div className="relative w-full h-full rounded-2xl overflow-hidden">
                                        <Image
                                            src={category.imageUrl}
                                            alt={`${category.name} kategorisi`}
                                            fill
                                            className="object-cover transform group-hover:scale-110 transition-transform duration-300"
                                        />
                                    </div>
                                </div>

                                {/* Category Name */}
                                <h3 className="text-lg font-semibold text-gray-800 text-center mb-2
                                             group-hover:text-orange-600 transition-colors duration-300">
                                    {category.name}
                                </h3>

                                {/* Product Count - İsteğe bağlı */}
                                {category.productCount && (
                                    <span className="text-sm text-gray-500">
                                        {category.productCount} ürün
                                    </span>
                                )}

                                {/* Hover Indicator */}
                                <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-8 h-1 rounded-full
                                              bg-orange-500 opacity-0 transform translate-y-4
                                              group-hover:opacity-100 group-hover:translate-y-0
                                              transition-all duration-300"/>
                            </div>
                        </Link>
                    ))}
                </div>
            ) : (
                <div className="flex flex-col items-center justify-center py-12">
                    <FaSpinner className="animate-spin text-4xl text-orange-600 mb-4"/>
                    <p className="text-gray-600 text-lg">Kategoriler Yükleniyor</p>
                </div>
            )}

            {/* Alt Bilgi - İsteğe bağlı */}
            <div className="text-center mt-12 text-sm text-gray-500">
                En sevdiğiniz lezzetleri kategoriler arasından seçebilirsiniz
            </div>
            <div className="text-center mt-8">
<Link href="/categories" className="inline-block bg-orange-600 text-white px-6 py-3 rounded-full text-lg font-semibold transition-all duration-300 hover:bg-orange-700">
    Tüm Kategorileri Gör
</Link>
            </div>
        </div>
    </div>
);

export default CategoryList;