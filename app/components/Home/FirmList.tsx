import Link from 'next/link';
import React from 'react';
import FirmCard from '../All-firms/FirmCard';

interface Firm {
    id: string;
    name: string;
    description: string;
    banner: string;
    visible: boolean;
}

interface FirmListProps {
    firms: Firm[];
}

const FirmList: React.FC<FirmListProps> = ({ firms }) => (
    <div className="container mx-auto py-10">
        <h2 className="text-2xl font-medium mb-6 text-center relative">
            Restoranlar
            <span className="block w-16 h-1 bg-orange-600 mx-auto mt-2"></span>
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {firms.length > 0 ? (
                firms.map((firm, index) => (
                    <FirmCard key={index} firm={firm} />
                ))
            ) : (
                <p className="text-center w-full">Firmalar yükleniyor</p>
            )}
        </div>
        <div className="text-center mt-10">
            <Link href="/all-firms"
                  className="text-lg font-medium text-black bg-orange-600 hover:bg-orange-700 py-2 px-4 rounded-full transition-colors duration-300">
                Tüm Restoranlar
            </Link>
        </div>
    </div>
);

export default FirmList;