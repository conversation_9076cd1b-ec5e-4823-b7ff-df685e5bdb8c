import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, X, Filter, ImageOff } from 'lucide-react';
import { useFirestoreData } from '@/hooks/useFirestoreData';
import Image from 'next/image';

type SearchResult = {
    id: string;
    name: string;
    description?: string;
    category?: string;
    banner?: string;
    images?: string[];
    imageUrl?: string;
    firmId?: string;
    visible?: boolean;
    price?: number;
};

type FilterType = 'all' | 'firms' | 'categories' | 'products';

const SearchBar: React.FC = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedTerm, setDebouncedTerm] = useState('');
    const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
    const [isFocused, setIsFocused] = useState(false);
    const [activeFilter, setActiveFilter] = useState<FilterType>('all');
    const [isMobileFiltersOpen, setIsMobileFiltersOpen] = useState(false);
    const router = useRouter();
    const { firms, categories, products } = useFirestoreData();

    const normalizeString = (str: string) => {
        return str
            .toLowerCase()
            .replace(/ç/g, 'c')
            .replace(/ğ/g, 'g')
            .replace(/ı/g, 'i')
            .replace(/ö/g, 'o')
            .replace(/ş/g, 's')
            .replace(/ü/g, 'u')
            .replace(/\s+/g, '')
            .replace(/[^a-z0-9]/g, '');
    };

    const searchInString = (searchIn: string, searchFor: string): boolean => {
        const normalizedSearchIn = normalizeString(searchIn);
        const normalizedSearchFor = normalizeString(searchFor);

        if (normalizedSearchIn === normalizedSearchFor) {
            return true;
        }

        if (normalizedSearchIn.includes(normalizedSearchFor)) {
            return true;
        }

        const searchInWords = searchIn.toLowerCase().split(/\s+/);
        const searchForWords = searchFor.toLowerCase().split(/\s+/);

        return searchForWords.every(searchWord => {
            const normalizedSearchWord = normalizeString(searchWord);
            return searchInWords.some(word => normalizeString(word).includes(normalizedSearchWord));
        });
    };

    useEffect(() => {
        if (debouncedTerm.length >= 2) {
            let results: SearchResult[] = [];

            // Get only visible firms
            const visibleFirms = firms.filter(firm => firm.visible !== false);
            const visibleFirmIds = new Set(visibleFirms.map(firm => firm.id));

            if (activeFilter === 'all' || activeFilter === 'firms') {
                results = [...results, ...visibleFirms.filter(firm =>
                    searchInString(firm.name, debouncedTerm)
                )];
            }

            if (activeFilter === 'all' || activeFilter === 'categories') {
                results = [...results, ...categories.filter(category =>
                    searchInString(category.name, debouncedTerm)
                )];
            }

            if (activeFilter === 'all' || activeFilter === 'products') {
                // Filter products that belong to visible firms
                const validProducts = products.filter(product =>
                    visibleFirmIds.has(product.firmId || '')
                );

                results = [...results, ...validProducts.filter(product =>
                    searchInString(product.name, debouncedTerm)
                )];
            }

            setSearchResults(results);
        } else {
            setSearchResults([]);
        }
    }, [debouncedTerm, firms, categories, products, activeFilter]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const searchContainer = document.getElementById('search-container');
            if (searchContainer && !searchContainer.contains(event.target as Node)) {
                setIsFocused(false);
                setIsMobileFiltersOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleResultClick = (result: SearchResult) => {
        if (result.id) {
            if (result.category) {
                router.push(`/products/${result.id}`);
            } else if (result.description) {
                router.push(`/firms/${result.id}`);
            } else if (result.name) {
                router.push(`/categories/${result.id}`);
            }
        }
        setSearchTerm('');
        setDebouncedTerm('');
        setIsFocused(false);
    };

    const getResultImage = (result: SearchResult) => {
        let imageUrl = '';

        if (result.category && result.images && result.images[0]) {
            imageUrl = result.images[0];
        } else if (result.description && result.banner) {
            imageUrl = result.banner;
        } else if (result.imageUrl) {
            imageUrl = result.imageUrl;
        }

        if (imageUrl) {
            return (
                <div className="relative w-10 h-10 md:w-12 md:h-12 rounded-xl overflow-hidden bg-gray-100">
                    <Image
                        src={imageUrl}
                        alt={result.name}
                        fill
                        className="object-cover transition-all duration-200"
                        sizes="(max-width: 768px) 40px, 48px"
                        onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const parent = target.parentElement;
                            if (parent) {
                                parent.classList.add('flex', 'items-center', 'justify-center');
                                const fallbackIcon = document.createElement('div');
                                fallbackIcon.innerHTML = '<svg class="w-5 h-5 md:w-6 md:h-6 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><polyline points="21 15 16 10 5 21"/></svg>';
                                parent.appendChild(fallbackIcon);
                            }
                        }}
                    />
                </div>
            );
        }

        return (
            <div className="w-10 h-10 md:w-12 md:h-12 rounded-xl overflow-hidden bg-gray-100 flex items-center justify-center">
                <ImageOff className="w-5 h-5 md:w-6 md:h-6 text-gray-400" />
            </div>
        );
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            setDebouncedTerm(searchTerm);
            setIsFocused(true);
        }
    };

    return (
        <div id="search-container" className="container mx-auto py-4 px-3 md:py-10 md:px-0 relative">
            <div className="relative z-10 mb-3 md:mb-4">
                <div className="hidden md:flex items-center justify-center gap-2 mb-6">
                    <nav className="flex gap-1 bg-gray-100/50 backdrop-blur-lg p-1 rounded-xl">
                        {['all', 'firms', 'categories', 'products'].map((filter) => (
                            <button
                                key={filter}
                                onClick={() => setActiveFilter(filter as FilterType)}
                                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                                    ${activeFilter === filter
                                    ? 'bg-white text-gray-900 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                                }`}
                            >
                                {filter === 'all' ? 'Tümü' :
                                    filter === 'firms' ? 'Firmalar' :
                                        filter === 'categories' ? 'Kategoriler' : 'Ürünler'}
                            </button>
                        ))}
                    </nav>
                </div>

                <div className="md:hidden">
                    <button
                        onClick={() => setIsMobileFiltersOpen(!isMobileFiltersOpen)}
                        className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-gray-100/50 backdrop-blur-lg text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                        <Filter className="w-4 h-4" />
                        <span className="text-sm font-medium">
                            {activeFilter === 'all' ? 'Tümü' :
                                activeFilter === 'firms' ? 'Firmalar' :
                                    activeFilter === 'categories' ? 'Kategoriler' : 'Ürünler'}
                        </span>
                    </button>

                    {isMobileFiltersOpen && (
                        <div className="absolute top-10 left-0 right-0 bg-white rounded-lg shadow-xl border border-gray-100 p-2 z-50">
                            <div className="flex flex-col gap-1">
                                {['all', 'firms', 'categories', 'products'].map((filter) => (
                                    <button
                                        key={filter}
                                        onClick={() => {
                                            setActiveFilter(filter as FilterType);
                                            setIsMobileFiltersOpen(false);
                                        }}
                                        className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 text-left
                                            ${activeFilter === filter
                                            ? 'bg-gray-100 text-gray-900'
                                            : 'text-gray-600 hover:bg-gray-50'
                                        }`}
                                    >
                                        {filter === 'all' ? 'Tümü' :
                                            filter === 'firms' ? 'Firmalar' :
                                                filter === 'categories' ? 'Kategoriler' : 'Ürünler'}
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-violet-400/20 via-orange-300/20 to-pink-400/20 rounded-xl md:rounded-2xl blur-xl"></div>
                <div className="relative bg-white/80 backdrop-blur-xl rounded-xl md:rounded-2xl shadow-lg border border-gray-200/50">
                    <div className="flex flex-col">
                        <div className="flex items-center px-3 md:px-4 h-12 md:h-16">
                            <Search className="w-4 h-4 md:w-5 md:h-5 text-gray-400" />
                            <input
                                type="text"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                onFocus={() => setIsFocused(true)}
                                onKeyDown={handleKeyDown}
                                placeholder="Firma, kategori veya ürün ara..."
                                className="flex-1 ml-2 md:ml-3 bg-transparent text-gray-900 placeholder-gray-400 outline-none text-sm md:text-lg"
                            />
                            {searchTerm && (
                                <button
                                    onClick={() => {
                                        setSearchTerm('');
                                        setDebouncedTerm('');
                                    }}
                                    className="p-1 md:p-1.5 hover:bg-gray-100 rounded-full transition-colors duration-200"
                                >
                                    <X className="w-4 h-4 text-gray-400" />
                                </button>
                            )}
                        </div>
                        <div className="px-3 md:px-4 pb-2 text-xs text-gray-500 flex items-center gap-2">
                            <kbd className="px-2 py-1 bg-gray-100 rounded-md text-gray-600 font-mono">Enter</kbd>
                            <span>tuşuna basarak arama yapabilirsiniz</span>
                        </div>
                    </div>
                </div>
            </div>

            {searchResults.length > 0 && isFocused && debouncedTerm.length >= 2 && (
                <div className="fixed md:absolute left-0 right-0 md:left-1/2 md:-translate-x-1/2 bottom-0 md:bottom-auto md:top-full mt-0 md:mt-2 bg-white/90 md:bg-white/80 backdrop-blur-xl rounded-t-xl md:rounded-xl shadow-lg border-t md:border border-gray-200/50 overflow-hidden z-50 md:max-w-3xl">
                    <div className="p-2 bg-gray-50/50 border-b border-gray-100">
                        <p className="text-sm text-gray-600">
                            <span className="font-medium">{searchResults.length}</span> sonuç bulundu
                        </p>
                    </div>
                    <ul className="divide-y divide-gray-100/50 max-h-[50vh] md:max-h-[60vh] overflow-y-auto">
                        {searchResults.map((item, index) => {
                            const relatedFirm = item.category ? firms.find(f => f.id === item.firmId) : null;

                            return (
                                <li
                                    key={index}
                                    onClick={() => handleResultClick(item)}
                                    className="group flex items-center gap-3 md:gap-4 px-3 md:px-4 py-2.5 md:py-3 hover:bg-gray-50/80 cursor-pointer transition-all duration-200"
                                >
                                    {getResultImage(item)}

                                    <div className="flex-1 min-w-0">
                                        <div className="flex flex-col gap-1">
                                            <div className="flex flex-col md:flex-row md:items-center gap-1 md:gap-2">
                                                <span className="font-medium text-gray-900 truncate text-sm md:text-base">
                                                    {item.name}
                                                </span>
                                                <span className="inline-flex items-center px-1.5 md:px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                                    {item.category ? 'Ürün' : item.description ? 'Firma' : 'Kategori'}
                                                </span>
                                            </div>

                                            {item.price && (
                                                <span className="text-xs md:text-sm text-gray-600">
                                                    {item.price} ₺
                                                </span>
                                            )}

                                            <div className="flex flex-col gap-0.5">
                                                {relatedFirm && (
                                                    <span className="text-xs md:text-sm text-gray-600 truncate">
                                                        {relatedFirm.name}
                                                    </span>
                                                )}
                                                {item.description && (
                                                    <p className="text-xs md:text-sm text-gray-500 truncate">
                                                        {item.description}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                        <svg
                                            className="w-4 h-4 text-gray-400"
                                            fill="none"
                                            strokeWidth={2}
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                d="M8.25 4.5l7.5 7.5-7.5 7.5"
                                            />
                                        </svg>
                                    </div>
                                </li>
                            );
                        })}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default SearchBar;