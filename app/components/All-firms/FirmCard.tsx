'use client';
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaMoneyBillWave, FaTruck, FaClock, FaStar, FaStoreAlt, FaStarHalfAlt } from 'react-icons/fa';
import { db } from '@/config/firebaseConfig';
import { collection, getDocs } from 'firebase/firestore';

interface Review {
    rating: number;
    comment: string;
}

interface OrderItem {
    id: string;
    firmId: string;
    productName: string;
    review?: Review;
}

interface Order {
    id: string;
    items: OrderItem[];
    firstName: string;
}

interface Firm {
    id: string;
    name: string;
    description: string;
    banner: string;
    visible: boolean;
    rating?: number;
    preparationTime?: string;
}

interface FirmCardProps {
    firm: Firm;
}

const FirmCard: React.FC<FirmCardProps> = React.memo(({ firm }) => {
    const [averageRating, setAverageRating] = React.useState<number | undefined>(firm.rating);
    const [totalReviews, setTotalReviews] = React.useState<number>(0);

    React.useEffect(() => {
        const fetchReviews = async () => {
            if (!firm.id) return;

            try {
                const ordersRef = collection(db, 'orders');
                const ordersSnapshot = await getDocs(ordersRef);
                const fetchedReviews: Review[] = [];

                for (const orderDoc of ordersSnapshot.docs) {
                    const orderData = orderDoc.data() as Order;
                    const relevantItems = orderData.items.filter(item =>
                        item.firmId === firm.id &&
                        item.review?.rating &&
                        item.review?.comment
                    );

                    relevantItems.forEach(item => {
                        if (item.review) {
                            fetchedReviews.push(item.review);
                        }
                    });
                }

                setTotalReviews(fetchedReviews.length);

                if (fetchedReviews.length > 0) {
                    const avgRating = fetchedReviews.reduce((acc, review) => acc + review.rating, 0) / fetchedReviews.length;
                    setAverageRating(Number(avgRating.toFixed(1)));
                } else {
                    setAverageRating(0);
                }
            } catch (error) {
                console.error('Error fetching reviews:', error);
                setAverageRating(0);
            }
        };

        fetchReviews();
    }, [firm.id]);

    const renderStars = (rating: number) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;

        // Tam yıldızlar
        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <FaStar key={`star-${i}`} className="text-yellow-400" />
            );
        }

        // Yarım yıldız
        if (hasHalfStar) {
            stars.push(
                <FaStarHalfAlt key="half-star" className="text-yellow-400" />
            );
        }

        // Boş yıldızlar
        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(
                <FaStar
                    key={`empty-star-${i}`}
                    className="text-gray-300"
                />
            );
        }

        return stars;
    };

    return (
        <Link
            href={firm.visible ? `/firms/${firm.id}` : '#'}
            className="group relative bg-white rounded-2xl overflow-hidden border border-gray-200 hover:shadow-xl hover:-translate-y-1 transition-all duration-300"
        >
            {/* Restoran Banner */}
            <div className="relative h-48 overflow-hidden">
                <Image
                    src={firm.banner}
                    alt={firm.name}
                    width={400}
                    height={200}
                    className={`w-full h-full object-cover transition-transform duration-500 ${
                        firm.visible ? 'group-hover:scale-110' : 'grayscale'
                    }`}
                />

                {/* Banner Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

                {/* Durum Badge */}
                <div className={`absolute top-4 left-4 px-3 py-1.5 rounded-full text-sm font-medium 
                               flex items-center gap-2 ${
                    firm.visible
                        ? 'bg-green-500 text-white'
                        : 'bg-red-500 text-white'
                }`}>
                    <div className={`w-2 h-2 rounded-full ${
                        firm.visible ? 'bg-white animate-pulse' : 'bg-white'
                    }`} />
                    {firm.visible ? 'Açık' : 'Kapalı'}
                </div>

                {/* Restoran İsmi ve Açıklaması */}
                <div className="absolute bottom-0 left-0 right-0 p-4">
                    <h3 className="text-xl font-bold text-white mb-1 line-clamp-1">
                        {firm.name}
                    </h3>
                    {firm.description && (
                        <p className="text-white/90 text-sm line-clamp-2">
                            {firm.description}
                        </p>
                    )}
                </div>
            </div>

            {/* Alt Bilgiler */}
            <div className="p-4 relative z-10 bg-white">
                {/* İstatistikler */}
                <div className="flex items-center gap-4 mb-4 text-sm">
                    <div className="flex items-center gap-1.5">
                        <div className="flex">
                            {averageRating !== undefined ? renderStars(averageRating) : renderStars(0)}
                        </div>
                        <span className="font-medium text-gray-700">
                            {averageRating !== undefined ? averageRating : 0} ({totalReviews})
                        </span>
                    </div>
                    {firm.preparationTime && (
                        <div className="flex items-center gap-1.5 text-gray-600">
                            <FaClock className="text-orange-500" />
                            <span>{firm.preparationTime}</span>
                        </div>
                    )}
                </div>

                {/* Teslimat Bilgileri */}
                <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-4">
                        {/* Minimum Tutar */}
                        <div className="flex items-center gap-2 text-gray-700">
                            <div className="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center">
                                <FaMoneyBillWave className="text-orange-600" />
                            </div>
                            <div className="flex flex-col">
                                <span className="text-xs text-gray-500">Min Tutar</span>
                                <span className="font-medium">100 ₺</span>
                            </div>
                        </div>

                        {/* Teslimat */}
                        <div className="flex items-center gap-2 text-gray-700">
                            <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                <FaTruck className="text-green-600" />
                            </div>
                            <div className="flex flex-col">
                                <span className="text-xs text-gray-500">Teslimat</span>
                                <span className="font-medium">Ücretsiz</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Kapalı Overlay */}
            {!firm.visible && (
                <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute inset-0 h-48 bg-black/45 backdrop-blur-xs" />
                    <div className="absolute top-24 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-2">
                        <FaStoreAlt className="text-3xl text-white" />
                        <span className="text-white font-medium px-4 py-1 rounded-full border border-white/30 backdrop-blur-sm">
                            Şu anda kapalı
                        </span>
                    </div>
                </div>
            )}
        </Link>
    );
});

FirmCard.displayName = 'FirmCard';

export default FirmCard;