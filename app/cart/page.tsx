'use client';
import React, {useEffect, useState} from 'react';
import {getAuth} from 'firebase/auth';
import {db} from '@/config/firebaseConfig';
import {collection, onSnapshot, doc, updateDoc, deleteDoc, getDoc} from 'firebase/firestore';
import {useRouter} from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import {FaMinus, FaPlus, FaStore, FaTag} from 'react-icons/fa';

interface CartItem {
    id: string;
    productId?: string;
    productName: string;
    price: number;
    quantity: number;
    imageURL: string;
    firmId: string;
    categoryId: string;
    firmName?: string;
    categoryName?: string;
    itemStatus?: string;
    selectedFeatures?: { name: string; price: number | string }[];
}

interface FirmData {
    name: string;
    visible: boolean;
}

export default function CartPage() {
    const [cartItems, setCartItems] = useState<CartItem[]>([]);
    const [totalPrice, setTotalPrice] = useState<number>(0);
    const [firmTotals, setFirmTotals] = useState<{ [key: string]: number }>({});
    const [errorMessage, setErrorMessage] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(true);
    const [invisibleFirms, setInvisibleFirms] = useState<string[]>([]);
    const auth = getAuth();
    const router = useRouter();

    useEffect(() => {
        const user = auth.currentUser;
        if (!user) {
            router.push('/signin');
            return;
        }

        const cartRef = collection(db, 'users', user.uid, 'activeCartWeb', 'cart', 'items');
        let firmUnsubscribes: (() => void)[] = [];

        const unsubscribe = onSnapshot(cartRef, async (snapshot) => {
            if (snapshot.empty) {
                setCartItems([]);
                setTotalPrice(0);
                setFirmTotals({});
                setInvisibleFirms([]);
                setLoading(false);
                return;
            }

            const items = snapshot.docs.map(doc => ({id: doc.id, ...doc.data()})) as CartItem[];
            const invisibleFirmsList: string[] = [];

            // Önceki dinleyicileri temizle
            firmUnsubscribes.forEach(unsubscribe => unsubscribe());
            firmUnsubscribes = [];

            // Her firma için anlık dinleyici ekle
            const uniqueFirmIds = [...new Set(items.map(item => item.firmId))];
            uniqueFirmIds.forEach(firmId => {
                const firmRef = doc(db, 'firms', firmId);
                const unsubscribeFirm = onSnapshot(firmRef, (firmDoc) => {
                    if (firmDoc.exists()) {
                        const firmData = firmDoc.data() as FirmData;
                        if (!firmData.visible) {
                            setInvisibleFirms(prev => {
                                if (!prev.includes(firmData.name)) {
                                    return [...prev, firmData.name];
                                }
                                return prev;
                            });
                        } else {
                            setInvisibleFirms(prev => prev.filter(name => name !== firmData.name));
                        }
                    }
                });
                firmUnsubscribes.push(unsubscribeFirm);
            });

            // Fetch firm and category names
            for (const item of items) {
                const firmDoc = await getDoc(doc(db, 'firms', item.firmId));
                const categoryDoc = await getDoc(doc(db, 'categories', item.categoryId));
                
                if (firmDoc.exists()) {
                    const firmData = firmDoc.data() as FirmData;
                    item.firmName = firmData.name;
                    
                    if (!firmData.visible) {
                        invisibleFirmsList.push(firmData.name);
                    }
                } else {
                    item.firmName = 'Unknown Firm';
                }
                
                item.categoryName = categoryDoc.exists() ? categoryDoc.data().name : 'Unknown Category';
            }

            setCartItems(items);

            const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
            setTotalPrice(total);

            const firmTotals = items.reduce((totals, item) => {
                totals[item.firmId] = (totals[item.firmId] || 0) + item.price * item.quantity;
                return totals;
            }, {} as { [key: string]: number });
            setFirmTotals(firmTotals);
            setLoading(false);
        });

        return () => {
            unsubscribe();
            firmUnsubscribes.forEach(unsubscribe => unsubscribe());
        };
    }, [auth, router]);

    const updateQuantity = async (itemId: string, newQuantity: number) => {
        const user = auth.currentUser;
        if (!user) {
            router.push('/signin');
            return;
        }

        if (newQuantity < 0) return;

        try {
            const itemRef = doc(db, 'users', user.uid, 'activeCartWeb', 'cart', 'items', itemId);
            if (newQuantity === 0) {
                await deleteDoc(itemRef);
            } else {
                await updateDoc(itemRef, {quantity: newQuantity});
            }
            setErrorMessage('');
        } catch (error) {
            console.error('Error updating quantity:', error);
            setErrorMessage('Ürün miktarı güncellenirken bir hata oluştu. Lütfen tekrar deneyin.');
        }
    };

    const handleCompleteOrder = async () => {
        // Sipariş tamamlanmadan önce firma durumlarını tekrar kontrol et
        const invisibleFirmsList: string[] = [];
        
        for (const item of cartItems) {
            const firmDoc = await getDoc(doc(db, 'firms', item.firmId));
            if (firmDoc.exists()) {
                const firmData = firmDoc.data() as FirmData;
                if (!firmData.visible) {
                    invisibleFirmsList.push(firmData.name);
                }
            }
        }

        // Görünmez firma varsa uyarı ver
        if (invisibleFirmsList.length > 0) {
            setErrorMessage(`Aşağıdaki firma(lar) artık hizmet vermiyor: ${invisibleFirmsList.join(', ')}. Lütfen bu ürünleri sepetinizden kaldırın.`);
            return;
        }

        const firmsBelowMinimum = Object.entries(firmTotals).filter(([, total]) => total < 100);
        if (firmsBelowMinimum.length > 0) {
            setErrorMessage('Her firmadan en az 100 TL\'lik sipariş vermelisiniz.');
            return;
        }
        
        router.push('/complete-order');
    };

    const removeInvisibleFirmItems = async () => {
        const user = auth.currentUser;
        if (!user) {
            router.push('/signin');
            return;
        }

        try {
            // Görünmez firmaların ürünlerini filtrele
            const itemsToRemove = cartItems.filter(item => 
                item.firmName && invisibleFirms.includes(item.firmName)
            );

            // Her ürünü sil
            for (const item of itemsToRemove) {
                const itemRef = doc(db, 'users', user.uid, 'activeCartWeb', 'cart', 'items', item.id);
                await deleteDoc(itemRef);
            }

            setErrorMessage('');
        } catch (error) {
            console.error('Error removing items:', error);
            setErrorMessage('Ürünler silinirken bir hata oluştu. Lütfen tekrar deneyin.');
        }
    };

    const removeFirmItems = async (firmId: string) => {
        const user = auth.currentUser;
        if (!user) {
            router.push('/signin');
            return;
        }

        try {
            // Bu firmaya ait tüm ürünleri bul
            const firmItems = cartItems.filter(item => item.firmId === firmId);
            
            // Her ürünü sil
            const promises = firmItems.map(item => {
                const itemRef = doc(db, 'users', user.uid, 'activeCartWeb', 'cart', 'items', item.id);
                return deleteDoc(itemRef);
            });

            await Promise.all(promises);
            setErrorMessage('');
        } catch (error) {
            console.error('Error removing firm items:', error);
            setErrorMessage('Firma ürünleri silinirken bir hata oluştu. Lütfen tekrar deneyin.');
        }
    };

    return (
        <div className="container mx-auto py-10 px-4">
            <h1 className="text-3xl font-bold mb-6">Sepetim</h1>
            {errorMessage && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
                    <div className="flex items-center justify-between gap-4">
                        <span className="block sm:inline flex-1">{errorMessage}</span>
                        {invisibleFirms.length > 0 && (
                            <button
                                onClick={removeInvisibleFirmItems}
                                className="whitespace-nowrap px-6 py-2.5 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors flex-shrink-0"
                            >
                                Tüm Kapalı Firmaları Çıkar
                            </button>
                        )}
                    </div>
                </div>
            )}

            {loading ? (
                <div className="text-center py-16">
                    <p className="text-xl text-gray-600 mb-4">Yükleniyor...</p>
                </div>
            ) : cartItems.length === 0 ? (
                <div className="text-center py-16">
                    <p className="text-xl text-gray-600 mb-4">Sepetinizde ürün bulunmamaktadır.</p>
                    <Link
                        href="/"
                        className="inline-block px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                    >
                        Alışverişe Başla
                    </Link>
                </div>
            ) : (
                <div className="space-y-8">
                    {/* Firma bazlı gruplandırma */}
                    {Object.entries(cartItems.reduce((groups, item) => {
                        if (!groups[item.firmId]) {
                            groups[item.firmId] = [];
                        }
                        groups[item.firmId].push(item);
                        return groups;
                    }, {} as { [key: string]: CartItem[] })).map(([firmId, items]) => (
                        <div key={firmId} className="bg-white rounded-xl shadow-lg overflow-hidden">
                            {/* Firma Başlığı */}
                            <Link
                                href={`/firms/${firmId}`}
                                className={`block p-4 ${!items[0].firmName || (items[0].firmName && invisibleFirms.includes(items[0].firmName)) 
                                    ? 'bg-red-50' 
                                    : 'bg-gray-50'} border-b hover:bg-gray-100 transition-colors`}
                            >
                                <div className="flex items-center gap-2 text-lg font-semibold">
                                    <FaStore className={items[0].firmName && invisibleFirms.includes(items[0].firmName) 
                                        ? "text-red-600" 
                                        : "text-orange-600"}/>
                                    <span>{items[0].firmName || 'Bilinmeyen Firma'}</span>
                                    <span className="text-sm font-normal text-gray-500">
                                        (Firma Toplamı: {firmTotals[firmId]} TL)
                                    </span>
                                    {items[0].firmName && invisibleFirms.includes(items[0].firmName) && (
                                        <span className="text-sm font-medium text-red-600 ml-2">
                                            (Bu firma şu anda hizmet vermiyor)
                                        </span>
                                    )}
                                </div>
                            </Link>

                            {items[0].firmName && invisibleFirms.includes(items[0].firmName) && (
                                <div className="p-4 bg-red-50 border-b border-red-100">
                                    <div className="flex items-center justify-between gap-4">
                                        <p className="text-sm text-red-700 flex items-center gap-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                            </svg>
                                            Bu firma şu an hizmet vermemektedir
                                        </p>
                                        <button
                                            onClick={() => removeFirmItems(items[0].firmId)}
                                            className="whitespace-nowrap px-6 py-2.5 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors flex-shrink-0"
                                        >
                                            Bu Firmayı Sepetten Çıkar
                                        </button>
                                    </div>
                                </div>
                            )}

                            {/* Firma Ürünleri */}
                            <ul className="divide-y divide-gray-100">
                                {items.map(item => (
                                    <li key={item.id} className="p-4 hover:bg-gray-50 transition-colors">
                                        <div className="flex gap-4">
                                            {/* Ürün Görseli */}
                                            <div className="w-24 h-24 flex-shrink-0">
                                                <Image
                                                    src={item.imageURL}
                                                    alt={item.productName}
                                                    width={96}
                                                    height={96}
                                                    className="w-full h-full object-cover rounded-lg"
                                                />
                                            </div>

                                            {/* Ürün Detayları */}
                                            <div className="flex-1">
                                                <h3 className="text-lg font-semibold mb-1">{item.productName}</h3>

                                                {/* Kategori Linki */}
                                                <Link
                                                    href={`/firms/${firmId}?category=${item.categoryId}`}
                                                    className="inline-flex items-center gap-1 text-sm text-gray-600 hover:text-orange-600 transition-colors mb-2"
                                                >
                                                    <FaTag className="text-xs"/>
                                                    {item.categoryName}
                                                </Link>

                                                {/* Seçilen Özellikler */}
                                                {item.selectedFeatures && item.selectedFeatures.length > 0 && (
                                                    <div className="mt-2 space-y-1">
                                                        {item.selectedFeatures.map((feature, index) => (
                                                            <div key={index} className="text-sm text-gray-600 flex items-center gap-2">
                                                                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full"></span>
                                                                <span>{feature.name}: {feature.price} TL</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                )}

                                                {/* Fiyat ve Miktar Kontrolü */}
                                                <div className="flex items-center justify-between mt-4">
                                                    <div className="flex items-center gap-4">
                                                        <button
                                                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                                            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors"
                                                        >
                                                            <FaMinus size={12}/>
                                                        </button>
                                                        <span className="font-medium">{item.quantity}</span>
                                                        <button
                                                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                                            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors"
                                                        >
                                                            <FaPlus size={12}/>
                                                        </button>
                                                    </div>
                                                    <div className="text-right">
                                                        <div className="text-lg font-semibold text-orange-600">
                                                            {item.price * item.quantity} TL
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {item.price} TL / adet
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                ))}
                            </ul>

                            {/* Firma Minimum Sipariş Uyarısı */}
                            {firmTotals[firmId] < 100 && (
                                <div className="p-4 bg-yellow-50 border-t border-yellow-100">
                                    <p className="text-sm text-yellow-700">
                                        Bu firmadan minimum 100 TL&apos;lik sipariş vermelisiniz.
                                        Kalan: {(100 - firmTotals[firmId]).toFixed(2)} TL
                                    </p>
                                </div>
                            )}
                        </div>
                    ))}

                    {/* Toplam ve Sipariş Tamamlama */}
                    <div className="sticky bottom-0 bg-white shadow-lg rounded-t-xl p-4 border-t">
                        <div className="flex items-center justify-between">
                            <div>
                                <div className="text-sm text-gray-600">Toplam Tutar</div>
                                <div className="text-2xl font-bold text-orange-600">{totalPrice} TL</div>
                            </div>
                            <button
                                className="px-8 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700
                                         transition-colors font-semibold disabled:bg-gray-400 disabled:cursor-not-allowed"
                                onClick={handleCompleteOrder}
                                disabled={Object.values(firmTotals).some(total => total < 100) || invisibleFirms.length > 0}
                            >
                                Siparişi Tamamla
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}