'use client';

import React, { useEffect, useState } from 'react';
import { getAuth } from 'firebase/auth';
import { db, Timestamp } from '@/config/firebaseConfig';
import {
    collection,
    getDocs,
    doc,
    getDoc,
    deleteDoc,
    serverTimestamp,
    setDoc,
    query,
    limit
} from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import AdressModal from '../components/AdressModal';
import {
    FaShoppingBag,
    FaTruck,
    FaCreditCard,
    FaComments,
    FaCheck,
    FaMapMarkerAlt,
    FaPhoneAlt,
    FaUser,
    FaStore,
    FaTag,
    FaTicketAlt,
    FaTimes
} from 'react-icons/fa';
import Link from "next/link";
import { validateCoupon, applyCoupon } from '@/utils/couponUtils';
import { Coupon } from '@/types/coupon';


interface CategoryData {
    name: string;

}
interface FirmData {
    name: string;

}


interface CartItem {
    id: string;
    productId?: string;
    productName: string;
    price: number;
    quantity: number;
    imageURL: string;
    firmId: string;
    categoryId: string;
    firmName?: string;
    categoryName?: string;
    itemStatus?: string;
    selectedFeatures?: { name: string; price: number | string }[];
}

interface Address {
    id: string;
    aciklama: string;
    bina_ve_daire: string;
    createdAt: Timestamp;
    location: {
        lat: number;
        lng: number;
    };
    mahalle: string;
    sokak: string;
    updatedAt: Timestamp;
}

interface UserData {
    firstName: string;
    lastName: string;
    phoneNumber: string;
    deliverTo: string;
    addresses: Address[];
}

const DELIVERY_INSTRUCTIONS = [
    'Zili Çalmayın',
    'Kapıda Bırakın',
    'Aramaktan Kaçının',
    'Güvenlikte Bırakın',
    'Geldiğinizde Arayın'
];

const PAYMENT_METHODS = [
    'Kapıda Nakit Ödeme',
    'Kapıda Kredi Kartı ile Ödeme'
];

const FIRST_ORDER_DISCOUNT = 10; // %10 indirim

export default function CompleteOrderPage() {
    // State definitions
    const [cartItems, setCartItems] = useState<CartItem[]>([]);
    const [totalPrice, setTotalPrice] = useState<number>(0);
    const [finalPrice, setFinalPrice] = useState<number>(0);
    const [deliveryInstructions, setDeliveryInstructions] = useState<string[]>([]);
    const [paymentMethod, setPaymentMethod] = useState<string>(PAYMENT_METHODS[0]);
    const [instructions, setInstructions] = useState<string>('');
    const [errorMessage, setErrorMessage] = useState<string>('');
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [isFirstOrder, setIsFirstOrder] = useState<boolean>(false);
    const [userData, setUserData] = useState<UserData | null>(null);
    const [isProcessing, setIsProcessing] = useState<boolean>(false);

    // Kupon state'leri
    const [couponCode, setCouponCode] = useState<string>('');
    const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
    const [couponDiscount, setCouponDiscount] = useState<number>(0);
    const [couponMessage, setCouponMessage] = useState<string>('');
    const [isCouponLoading, setIsCouponLoading] = useState<boolean>(false);

    const auth = getAuth();
    const router = useRouter();

    // Fetch cart items and check first order
    useEffect(() => {
        const fetchCartItemsAndCheckFirstOrder = async () => {
            const user = auth.currentUser;
            if (!user) {
                router.push('/signin');
                return;
            }

            try {
                // Fetch user data
                const userDocRef = doc(db, 'users', user.uid);
                const userDocSnap = await getDoc(userDocRef);
                if (userDocSnap.exists()) {
                    setUserData(userDocSnap.data() as UserData);
                }

                // Fetch cart items
                const cartRef = collection(db, 'users', user.uid, 'activeCartWeb', 'cart', 'items');
                const cartSnapshot = await getDocs(cartRef);
                const items: CartItem[] = [];

                for (const docSnap of cartSnapshot.docs) {
                    const itemData = docSnap.data();
                    console.log('Cart Item Data:', itemData);
                    
                    const item: CartItem = {
                        id: docSnap.id,
                        productId: itemData.productId,
                        productName: itemData.productName,
                        price: itemData.price,
                        quantity: itemData.quantity,
                        imageURL: itemData.imageURL,
                        firmId: itemData.firmId,
                        categoryId: itemData.categoryId,
                        selectedFeatures: itemData.selectedFeatures || [],
                        itemStatus: 'Sipariş Alındı'
                    };

                    // Firma ve kategori detaylarını getir
                    const firmDocRef = doc(db, 'firms', item.firmId);
                    const categoryDocRef = doc(db, 'categories', item.categoryId);

                    const [firmDoc, categoryDoc] = await Promise.all([
                        getDoc(firmDocRef),
                        getDoc(categoryDocRef)
                    ]);

                    if (firmDoc.exists()) {
                        const firmData = firmDoc.data() as FirmData;
                        item.firmName = firmData.name;
                    } else {
                        item.firmName = 'Unknown Firm';
                    }

                    if (categoryDoc.exists()) {
                        const categoryData = categoryDoc.data() as CategoryData;
                        item.categoryName = categoryData.name;
                    } else {
                        item.categoryName = 'Unknown Category';
                    }

                    items.push(item);
                }

                setCartItems(items);

                // Calculate prices
                const calculatedTotalPrice = items.reduce((sum, item) =>
                    sum + (item.price * item.quantity), 0);
                setTotalPrice(calculatedTotalPrice);

                // Check if first order
                const ordersRef = collection(db, 'users', user.uid, 'orders');
                const q = query(ordersRef, limit(1));
                const orderSnapshot = await getDocs(q);

                const firstOrder = orderSnapshot.empty;
                if (firstOrder) {
                    console.log('İlk sipariş tespit edildi');
                    setIsFirstOrder(true);
                }

                // Final price'ı hesapla (kupon indirimi dahil)
                // İlk sipariş durumunu direkt kullan, state güncellemesini bekleme
                let discount = 0;
                if (firstOrder && !appliedCoupon) {
                    discount = calculatedTotalPrice * FIRST_ORDER_DISCOUNT / 100;
                }
                setFinalPrice(calculatedTotalPrice - discount);

            } catch (error) {
                console.error('Veri getirme hatası:', error);
                setErrorMessage('Veriler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
            }
        };

        fetchCartItemsAndCheckFirstOrder();
    }, [auth, router]);

    // Final price hesaplama fonksiyonu
    const calculateFinalPrice = (basePrice: number) => {
        let discount = 0;

        // Kupon indirimi öncelikli
        if (appliedCoupon) {
            discount = couponDiscount;
        }
        // Kupon yoksa ve ilk siparişse %10 indirim
        else if (isFirstOrder) {
            discount = basePrice * FIRST_ORDER_DISCOUNT / 100;
        }

        setFinalPrice(basePrice - discount);
    };

    // Total price değiştiğinde final price'ı yeniden hesapla
    useEffect(() => {
        if (totalPrice > 0) {
            calculateFinalPrice(totalPrice);
        }
    }, [totalPrice, isFirstOrder, appliedCoupon, couponDiscount]);

    // Kupon uygulama fonksiyonu
    const handleApplyCoupon = async () => {
        if (!couponCode.trim()) {
            setCouponMessage('Lütfen kupon kodunu girin.');
            return;
        }

        const user = auth.currentUser;
        if (!user) return;

        setIsCouponLoading(true);
        setCouponMessage('');

        try {
            const result = await validateCoupon(couponCode.trim(), user.uid, totalPrice, isFirstOrder);

            if (result.isValid && result.coupon) {
                setAppliedCoupon(result.coupon);
                setCouponDiscount(result.discount);
                setCouponMessage(result.message);
                // Kupon indirimi direkt uygula
                setFinalPrice(totalPrice - result.discount);
            } else {
                setCouponMessage(result.message);
            }
        } catch (error) {
            console.error('Kupon uygulama hatası:', error);
            setCouponMessage('Kupon uygulanırken bir hata oluştu.');
        } finally {
            setIsCouponLoading(false);
        }
    };

    // Kupon kaldırma fonksiyonu
    const handleRemoveCoupon = () => {
        setAppliedCoupon(null);
        setCouponDiscount(0);
        setCouponCode('');
        setCouponMessage('');
        // Kupon kaldırıldığında ilk sipariş indirimi kontrol et
        const discount = isFirstOrder ? totalPrice * FIRST_ORDER_DISCOUNT / 100 : 0;
        setFinalPrice(totalPrice - discount);
    };

    const validateAddress = (userData: UserData | null): boolean => {
        if (!userData?.deliverTo || !userData?.addresses?.length) {
            setErrorMessage('Lütfen adres bilgilerinizi ekleyin. Yönlendiriliyorsunuz...');
            setIsModalOpen(true);
            setTimeout(() => {
                router.push('/account/addresses');
            }, 3000);
            return false;
        }
        return true;
    };

    const handleCompleteOrder = async () => {
        if (isProcessing) return;
        
        const user = auth.currentUser;
        if (!user) {
            router.push('/signin');
            return;
        }

        // Sepet boş mu kontrolü
        if (!cartItems || cartItems.length === 0) {
            setErrorMessage('Sepetinizde ürün bulunmamaktadır veya ürünler henüz yüklenmedi. Lütfen bekleyin ve tekrar deneyin.');
            setIsModalOpen(true);
            return;
        }

        if (!validateAddress(userData)) return;

        try {
            setIsProcessing(true);
            
            const orderId = doc(collection(db, 'orders')).id;

            const orderItems = cartItems.map(item => ({
                ...item,
                productId: item.productId || item.id,
                itemStatus: 'Sipariş Alındı'
            }));

            // İndirim hesaplama
            let discountApplied = 0;
            let discountType = '';

            if (appliedCoupon) {
                discountApplied = couponDiscount;
                discountType = 'coupon';
            } else if (isFirstOrder) {
                discountApplied = totalPrice * FIRST_ORDER_DISCOUNT / 100;
                discountType = 'firstOrder';
            }

            const orderData = {
                userId: user.uid,
                items: orderItems,
                totalPrice: totalPrice,
                discountApplied: discountApplied,
                discountType: discountType,
                appliedCoupon: appliedCoupon ? {
                    code: appliedCoupon.code,
                    discount: couponDiscount,
                    id: appliedCoupon.id
                } : null,
                finalPrice: finalPrice,
                isFirstOrder: isFirstOrder,
                deliverTo: userData?.deliverTo,
                addresses: userData?.addresses,
                deliveryInstructions,
                paymentMethod,
                instructions,
                orderDate: serverTimestamp(),
                firstName: userData?.firstName || 'Unknown',
                lastName: userData?.lastName || 'Unknown',
                phoneNumber: userData?.phoneNumber || 'Unknown',
                status: 'Sipariş Alındı'
            };

            // Save order to multiple locations
            await Promise.all([
                setDoc(doc(db, 'orders', orderId), orderData),
                setDoc(doc(db, 'users', user.uid, 'orders', orderId), orderData),
                ...cartItems.map(item =>
                    setDoc(doc(db, 'firms', item.firmId, 'orders', orderId), orderData)
                )
            ]);

            // Kupon kullanıldıysa, kupon kullanım bilgisini güncelle
            if (appliedCoupon) {
                await applyCoupon(appliedCoupon, user.uid);
            }

            // Kupon kullanıldıysa, kupon kullanım bilgisini güncelle
            if (appliedCoupon) {
                await applyCoupon(appliedCoupon, user.uid);
            }

            // Clear cart
            const cartRef = collection(db, 'users', user.uid, 'activeCartWeb', 'cart', 'items');
            const cartSnapshot = await getDocs(cartRef);
            await Promise.all(
                cartSnapshot.docs.map(doc => deleteDoc(doc.ref))
            );

            router.push('/order-confirmation');

        } catch (error) {
            console.error('Sipariş tamamlama hatası:', error);
            setErrorMessage('Sipariş işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.');
            setIsModalOpen(true);
        } finally {
            setIsProcessing(false); // İşlem bittiğinde veya hata olduğunda loading state'i false yap
        }
    };

    const toggleDeliveryInstruction = (instruction: string) => {
        setDeliveryInstructions(prev =>
            prev.includes(instruction)
                ? prev.filter(i => i !== instruction)
                : [...prev, instruction]
        );
    };


    return (
        <div className="min-h-screen bg-gray-50 py-10">
            <div className="container mx-auto px-4 max-w-6xl">
                <h1 className="text-3xl font-bold mb-8 text-gray-800">Siparişi Tamamla</h1>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Sol Kolon - Ürünler ve Form */}
                    <div className="lg:col-span-2 space-y-6">

                        {userData && (
                            <div className="bg-white rounded-xl shadow-md p-6">
                                <div className="flex items-center justify-between mb-6">
                                    <div className="flex items-center gap-4">
                                        <FaUser className="text-2xl text-orange-600" />
                                        <h2 className="text-xl font-semibold">Teslimat Bilgileri</h2>
                                    </div>
                                    <Link
                                        href="/account/addresses"
                                        className="text-orange-600 hover:text-orange-700 font-medium flex items-center gap-2
                         px-4 py-2 rounded-lg hover:bg-orange-50 transition-all"
                                    >
                                        <FaMapMarkerAlt />
                                        Adres Değiştir
                                    </Link>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-600">
                                    <div className="flex items-center gap-2">
                                        <FaUser className="text-gray-400" />
                                        <span>{userData.firstName} {userData.lastName}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <FaPhoneAlt className="text-gray-400" />
                                        <span>{userData.phoneNumber}</span>
                                    </div>
                                    {userData.deliverTo && userData.addresses && (
                                        <div className="md:col-span-2">
                                            <div className="flex items-center gap-2 mb-2">
                                                <div className="flex-1 flex items-center gap-2">
                                                    <FaMapMarkerAlt className="text-orange-600" />
                                                    <span className="font-medium text-gray-800">Teslimat Adresi</span>
                                                </div>
                                            </div>
                                            {userData.addresses.map(address =>
                                                address.id === userData.deliverTo && (
                                                    <div key={address.id}
                                                        className="ml-6 p-3 bg-orange-50 rounded-lg border border-orange-100">
                                                        <p className="font-medium text-gray-800">{address.mahalle}, {address.sokak}</p>
                                                        <p className="text-gray-600">{address.bina_ve_daire}</p>
                                                        {address.aciklama && (
                                                            <p className="text-sm text-gray-500 mt-2 pt-2 border-t border-orange-200">
                                                                {address.aciklama}
                                                            </p>
                                                        )}
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Ürünler */}
                        <div className="bg-white rounded-xl shadow-md overflow-hidden">
                            <div className="p-6 border-b">
                                <div className="flex items-center gap-4">
                                    <FaShoppingBag className="text-2xl text-orange-600" />
                                    <h2 className="text-xl font-semibold">Sipariş Detayları</h2>
                                </div>
                            </div>
                            <div className="divide-y">
                                {cartItems.map(item => (
                                    <div key={item.id} className="p-4 hover:bg-gray-50 transition-colors">
                                        <div className="flex gap-4">
                                            <Image
                                                src={item.imageURL}
                                                alt={item.productName}
                                                width={80}
                                                height={80}
                                                className="w-20 h-20 object-cover rounded-lg"
                                            />
                                            <div className="flex-1">
                                                <h3 className="font-semibold text-lg">{item.productName}</h3>
                                                <div className="flex items-center gap-6 text-sm text-gray-600 mt-1">
                                                    <div className="flex items-center gap-1">
                                                        <FaStore className="text-gray-400" />
                                                        <span>{item.firmName}</span>
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <FaTag className="text-gray-400" />
                                                        <span>{item.categoryName}</span>
                                                    </div>
                                                </div>
                                                <div className="mt-2 flex items-center justify-between">
                                                    <div className="text-gray-500">
                                                        {item.quantity} x {item.price.toFixed(2)} TL
                                                    </div>
                                                    <div className="font-medium text-orange-600">
                                                        {(item.price * item.quantity).toFixed(2)} TL
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Teslimat Talimatları */}
                        <div className="bg-white rounded-xl shadow-md p-6">
                            <div className="flex items-center gap-4 mb-6">
                                <FaTruck className="text-2xl text-orange-600" />
                                <h2 className="text-xl font-semibold">Teslimat Talimatları</h2>
                            </div>
                            <div className="flex flex-wrap gap-3">
                                {DELIVERY_INSTRUCTIONS.map(instruction => (
                                    <button
                                        key={instruction}
                                        onClick={() => toggleDeliveryInstruction(instruction)}
                                        className={`px-4 py-2 rounded-lg transition-all duration-300 flex items-center gap-2
                                            ${deliveryInstructions.includes(instruction)
                                                ? 'bg-orange-600 text-white shadow-md scale-105'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                                    >
                                        {deliveryInstructions.includes(instruction) && <FaCheck className="text-sm" />}
                                        {instruction}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Ödeme Yöntemi */}
                        <div className="bg-white rounded-xl shadow-md p-6">
                            <div className="flex items-center gap-4 mb-6">
                                <FaCreditCard className="text-2xl text-orange-600" />
                                <h2 className="text-xl font-semibold">Ödeme Yöntemi</h2>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {PAYMENT_METHODS.map(method => (
                                    <button
                                        key={method}
                                        onClick={() => setPaymentMethod(method)}
                                        className={`p-4 rounded-xl transition-all duration-300 flex items-center gap-3
                                            ${paymentMethod === method
                                                ? 'bg-orange-600 text-white shadow-md scale-105'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                                    >
                                        <FaCreditCard />
                                        {method}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Kupon Kodu */}
                        <div className="bg-white rounded-xl shadow-md p-6">
                            <div className="flex items-center gap-4 mb-6">
                                <FaTicketAlt className="text-2xl text-orange-600" />
                                <h2 className="text-xl font-semibold">Kupon Kodu</h2>
                            </div>

                            {!appliedCoupon ? (
                                <div className="space-y-4">
                                    <div className="flex gap-3">
                                        <input
                                            type="text"
                                            value={couponCode}
                                            onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                                            placeholder="Kupon kodunuzu girin"
                                            className="flex-1 p-3 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                            disabled={isCouponLoading}
                                        />
                                        <button
                                            onClick={handleApplyCoupon}
                                            disabled={isCouponLoading || !couponCode.trim()}
                                            className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                                                isCouponLoading || !couponCode.trim()
                                                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                                    : 'bg-orange-600 text-white hover:bg-orange-700'
                                            }`}
                                        >
                                            {isCouponLoading ? 'Kontrol Ediliyor...' : 'Uygula'}
                                        </button>
                                    </div>
                                    {couponMessage && (
                                        <p className={`text-sm ${appliedCoupon ? 'text-green-600' : 'text-red-600'}`}>
                                            {couponMessage}
                                        </p>
                                    )}
                                </div>
                            ) : (
                                <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <FaCheck className="text-green-600" />
                                            <div>
                                                <p className="font-medium text-green-800">
                                                    Kupon Uygulandı: {appliedCoupon.code}
                                                </p>
                                                <p className="text-sm text-green-600">
                                                    {couponDiscount.toFixed(2)} TL indirim
                                                </p>
                                            </div>
                                        </div>
                                        <button
                                            onClick={handleRemoveCoupon}
                                            className="text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                                        >
                                            <FaTimes />
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Ek Talimatlar */}
                        <div className="bg-white rounded-xl shadow-md p-6">
                            <div className="flex items-center gap-4 mb-6">
                                <FaComments className="text-2xl text-orange-600" />
                                <h2 className="text-xl font-semibold">Ek Talimatlar</h2>
                            </div>
                            <textarea
                                value={instructions}
                                onChange={(e) => setInstructions(e.target.value)}
                                placeholder="Varsa ek talimatlarınızı buraya yazabilirsiniz..."
                                className="w-full p-4 border rounded-xl focus:ring-2 focus:ring-orange-500
                                         focus:border-transparent min-h-[120px] resize-none"
                            />
                        </div>
                    </div>

                    {/* Sağ Kolon - Sipariş Özeti */}
                    <div className="lg:col-span-1">
                        <div className="bg-white rounded-xl shadow-md p-6 sticky top-6">
                            <h2 className="text-xl font-semibold mb-6">Sipariş Özeti</h2>

                            <div className="space-y-4">
                                <div className="flex justify-between text-gray-600">
                                    <span>Ara Toplam</span>
                                    <span>{totalPrice.toFixed(2)} TL</span>
                                </div>

                                {/* Kupon indirimi */}
                                {appliedCoupon && (
                                    <div className="flex justify-between text-green-600">
                                        <span>Kupon İndirimi ({appliedCoupon.code})</span>
                                        <span>-{couponDiscount.toFixed(2)} TL</span>
                                    </div>
                                )}

                                {/* İlk sipariş indirimi (sadece kupon yoksa) */}
                                {isFirstOrder && !appliedCoupon && (
                                    <div className="flex justify-between text-green-600">
                                        <span>İlk Sipariş İndirimi (%{FIRST_ORDER_DISCOUNT})</span>
                                        <span>-{(totalPrice * FIRST_ORDER_DISCOUNT / 100).toFixed(2)} TL</span>
                                    </div>
                                )}

                                <div className="pt-4 border-t">
                                    <div className="flex justify-between text-xl font-bold">
                                        <span>Toplam</span>
                                        <span className="text-orange-600">{finalPrice.toFixed(2)} TL</span>
                                    </div>

                                    {/* İndirim mesajları */}
                                    {appliedCoupon && (
                                        <p className="text-sm text-green-600 mt-2">
                                            {appliedCoupon.code} kuponu ile {couponDiscount.toFixed(2)} TL indirim!
                                        </p>
                                    )}
                                    {isFirstOrder && !appliedCoupon && (
                                        <p className="text-sm text-green-600 mt-2">
                                            İlk siparişinize özel %{FIRST_ORDER_DISCOUNT} indirim uygulandı!
                                        </p>
                                    )}
                                    {isFirstOrder && appliedCoupon && (
                                        <p className="text-sm text-blue-600 mt-2">
                                            İlk siparişiniz! Kupon indirimi uygulandı.
                                        </p>
                                    )}
                                </div>

                                <button
                                    onClick={handleCompleteOrder}
                                    disabled={isProcessing}
                                    className={`w-full py-4 ${
                                        isProcessing 
                                            ? 'bg-gray-400 cursor-not-allowed' 
                                            : 'bg-orange-600 hover:bg-orange-700'
                                    } text-white rounded-xl
                                    transition-colors duration-300 font-semibold text-lg mt-6
                                    flex items-center justify-center gap-2`}
                                >
                                    {isProcessing ? (
                                        <>
                                            <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
                                                <circle 
                                                    className="opacity-25" 
                                                    cx="12" 
                                                    cy="12" 
                                                    r="10" 
                                                    stroke="currentColor" 
                                                    strokeWidth="4"
                                                    fill="none"
                                                />
                                                <path 
                                                    className="opacity-75" 
                                                    fill="currentColor" 
                                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                />
                                            </svg>
                                            İşleniyor...
                                        </>
                                    ) : (
                                        <>
                                            <FaCheck />
                                            Siparişi Tamamla
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Address Modal */}
            <AdressModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title="Adres Bilgisi Eksik"
            >
                <p>{errorMessage}</p>
            </AdressModal>
        </div>
    );
}