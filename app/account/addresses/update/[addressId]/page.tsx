'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { getAuth } from 'firebase/auth';
import { app, db } from '@/config/firebaseConfig';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import AddressForm from '@/app/account/components/AddressForm';
import MapComponent from '@/app/account/components/MapComponent';
import { useMap } from '@/hooks/useMap';

interface Address {
    id: string;
    bina_ve_daire: string;
    mahalle: string;
    sokak: string;
    aciklama: string;
    latitude: number;
    longitude: number;
}

export default function UpdateAddressPage() {
    const { isLoaded, loadError, location, setLocation, mapContainerStyle, handleMapClick, mapRef } = useMap();
    const [binaVeDaire, setBinaVeDaire] = useState('');
    const [mahalle, setMahalle] = useState('');
    const [sokak, setSokak] = useState('');
    const [aciklama, setAciklama] = useState('');
    const router = useRouter();
    const { addressId } = useParams();
    const auth = getAuth(app);

    useEffect(() => {
        const fetchAddress = async () => {
            const user = auth.currentUser;
            if (user && typeof addressId === 'string') {
                const userRef = doc(db, 'users', user.uid);
                try {
                    const userDoc = await getDoc(userRef);
                    if (userDoc.exists()) {
                        const addresses: Address[] = userDoc.data().addresses || [];
                        const address = addresses.find(addr => addr.id === addressId);
                        if (address) {
                            setBinaVeDaire(address.bina_ve_daire);
                            setMahalle(address.mahalle);
                            setSokak(address.sokak);
                            setAciklama(address.aciklama);
                            setLocation({
                                lat: address.latitude,
                                lng: address.longitude
                            });
                        } else {
                            console.error('Adres bulunamadı: ', addressId);
                            router.push('/account/addresses');
                        }
                    } else {
                        console.error('Kullanıcı belgesi bulunamadı');
                        router.push('/signin');
                    }
                } catch (error) {
                    console.error('Adres yüklenirken hata oluştu: ', error);
                    router.push('/signin');
                }
            } else {
                console.error('Kullanıcı oturumu açmamış veya geçersiz addressId: ', addressId);
                router.push('/signin');
            }
        };

        fetchAddress();
    }, [auth, addressId, router, setLocation]);

    const handleUpdateAddress = async (e: React.FormEvent) => {
        e.preventDefault();
        const user = auth.currentUser;
        if (user && typeof addressId === 'string') {
            const userRef = doc(db, 'users', user.uid);
            try {
                const userDoc = await getDoc(userRef);
                if (userDoc.exists()) {
                    const addresses: Address[] = userDoc.data().addresses || [];
                    const updatedAddresses = addresses.map(addr =>
                        addr.id === addressId ? {
                            ...addr,
                            bina_ve_daire: binaVeDaire,
                            mahalle,
                            sokak,
                            aciklama,
                            latitude: location.lat,
                            longitude: location.lng,
                            updatedAt: new Date()
                        } : addr
                    );
                    await updateDoc(userRef, { addresses: updatedAddresses });
                    router.push('/account/addresses');
                } else {
                    console.error('Kullanıcı belgesi bulunamadı');
                }
            } catch (err) {
                console.error('Adres güncellenirken hata oluştu:', err);
            }
        } else {
            console.error('Kullanıcı girişi yapmamış veya adres ID bulunamadı');
        }
    };

    if (loadError) {
        return <div>Harita yüklenirken bir hata oluştu: {loadError.message}</div>;
    }

    return (
        <div className="flex items-center justify-center py-10">
            <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
                <h2 className="text-2xl font-bold mb-6 text-center">Adres Güncelle</h2>
                <AddressForm
                    sokak={sokak}
                    setSokak={setSokak}
                    mahalle={mahalle}
                    setMahalle={setMahalle}
                    binaVeDaire={binaVeDaire}
                    setBinaVeDaire={setBinaVeDaire}
                    aciklama={aciklama}
                    setAciklama={setAciklama}
                    handleAddAddress={handleUpdateAddress}
                />
                <div className="mb-4">
                    <label className="block text-gray-700 mb-2">Konum</label>
                    <label className="block text-gray-700 mb-2 text-sm">
                        Harita üzerinde adresinize giderek dokunarak(tıklayın) işaretleyebilirsiniz
                    </label>
                    <MapComponent
                        isLoaded={isLoaded}
                        location={location}
                        mapContainerStyle={mapContainerStyle}
                        handleMapClick={handleMapClick}
                        mapRef={mapRef}
                    />
                </div>
                <button
                    type="submit"
                    className="w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition-colors duration-300"
                    onClick={handleUpdateAddress}
                >
                    Güncelle
                </button>
            </div>
        </div>
    );
}
