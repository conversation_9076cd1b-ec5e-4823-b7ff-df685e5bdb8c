'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation'; // 'next/navigation' yerine 'next/router' kullanılmalı
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { app, db } from '@/config/firebaseConfig';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import Link from 'next/link';
import DeleteModal from '@/app/account/components/DeleteModal';

interface Address {
    id: string;
    bina_ve_daire: string;
    mahalle: string;
    sokak: string;
    aciklama: string;
}

interface UserDocData {
    addresses: Address[];
    deliverTo?: string;
}

export default function ListAddressesPage() {
    const [addresses, setAddresses] = useState<Address[]>([]);
    const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [addressToDelete, setAddressToDelete] = useState<string | null>(null);
    const router = useRouter();
    const auth = getAuth(app);

    const handleSetDeliverTo = useCallback(async (addressId: string) => {
        const user = auth.currentUser;
        if (!user) {
            console.error('Kullanıcı oturumu açmamış');
            return;
        }

        try {
            const userRef = doc(db, 'users', user.uid);
            await updateDoc(userRef, {
                deliverTo: addressId
            });
            setSelectedAddressId(addressId);
        } catch (err) {
            console.error('Adres ayarlanırken hata oluştu:', err);
        }
    }, [auth]);

    const handleDeleteAddress = useCallback(async () => {
        if (!addressToDelete) return;

        const user = auth.currentUser;
        if (!user) {
            console.error('Kullanıcı oturumu açmamış');
            return;
        }

        try {
            const userRef = doc(db, 'users', user.uid);
            const userDoc = await getDoc(userRef);
            if (userDoc.exists()) {
                const userData = userDoc.data() as UserDocData;
                const userAddresses = userData.addresses || [];
                const updatedAddresses = userAddresses.filter((addr: Address) => addr.id !== addressToDelete);
                await updateDoc(userRef, { addresses: updatedAddresses });
                setAddresses(updatedAddresses);
                if (selectedAddressId === addressToDelete) {
                    setSelectedAddressId(null);
                }
            }
        } catch (err) {
            console.error('Adres silinirken hata oluştu:', err);
        } finally {
            setIsDeleteModalOpen(false);
            setAddressToDelete(null);
        }
    }, [auth, addressToDelete, selectedAddressId]);

    const openDeleteModal = (addressId: string) => {
        setAddressToDelete(addressId);
        setIsDeleteModalOpen(true);
    };

    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, async (user) => {
            if (user) {
                const userRef = doc(db, 'users', user.uid);
                const userDoc = await getDoc(userRef);
                if (userDoc.exists()) {
                    const userData = userDoc.data() as UserDocData;
                    const userAddresses = userData.addresses || [];
                    setAddresses(userAddresses);
                    
                    const isDeliverToValid = userData.deliverTo && 
                        userAddresses.some(addr => addr.id === userData.deliverTo);

                    if (userAddresses.length > 0 && (!userData.deliverTo || !isDeliverToValid)) {
                        await handleSetDeliverTo(userAddresses[0].id);
                    } else {
                        setSelectedAddressId(userData.deliverTo || null);
                    }
                }
            } else {
                await router.push('/signin');
            }
        });

        return () => unsubscribe();
    }, [auth, router, handleSetDeliverTo]);

    return (
        <div className="flex flex-col items-center py-10">
            <div className="bg-white p-6 rounded-lg shadow-lg w-full">
                <div className="mb-6 flex justify-between items-center">
                    <h2 className="text-2xl font-bold">Adreslerim</h2>

                    <Link href="/account/addresses/add"
                          className="px-4 py-2 bg-blue-500 hover:bg-blue-700 rounded text-white">
                        Adres Ekle
                    </Link>
                </div>
                <h5 className="text-sm ">Sipariş verebilmek için en az 1 adres ekledikten sonra varsayılan adres seçmeniz gerekmektedir. </h5>
                <br/>

                <ul className="space-y-4">
                    {addresses.map((address) => (
                        <li key={address.id}
                            className={`flex justify-between items-center p-4 rounded-lg ${selectedAddressId === address.id ? 'bg-green-100' : 'bg-gray-50'}`}>
                            <div>
                                <p className="font-semibold">{address.bina_ve_daire}</p>
                                <p>{address.mahalle}</p>
                                <p>{address.sokak}</p>
                                <p className="text-sm text-gray-600">{address.aciklama}</p>
                            </div>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => handleSetDeliverTo(address.id)}
                                    className="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg"
                                >
                                    {selectedAddressId === address.id ? 'Varsayılan Adres' : 'Varsayılan Yap'}
                                </button>
                                <Link href={`/account/addresses/update/${address.id}`}
                                      className="px-4 py-2 bg-yellow-500 hover:bg-yellow-700 rounded text-white">
                                    Güncelle
                                </Link>
                                <button
                                    onClick={() => openDeleteModal(address.id)}
                                    className="px-4 py-2 bg-red-500 hover:bg-red-700 text-white rounded-lg"
                                >
                                    Sil
                                </button>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
            <DeleteModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onDelete={handleDeleteAddress}
                title="Adresi Sil"
                message="Bu adresi silmek istediğinizden emin misiniz?"
            />
        </div>
    );
}
