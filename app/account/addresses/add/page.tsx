'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { doc, getDoc, updateDoc, arrayUnion } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';
import AddressForm from '@/app/account/components/AddressForm';
import MapComponent from '@/app/account/components/MapComponent';
import { useAuth } from '@/hooks/useAuth';
import { useMap } from '@/hooks/useMap';

export default function AddAddressPage() {
    const { user } = useAuth();
    const { isLoaded, loadError, location, mapContainerStyle, handleMapClick, mapRef } = useMap();
    const router = useRouter();

    const [binaVeDaire, setBinaVeDaire] = useState('');
    const [mahalle, setMahalle] = useState('');
    const [sokak, setSokak] = useState('');
    const [aciklama, setAciklama] = useState('');

    const handleAddAddress = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!user) {
            console.error('Kullanıcı oturumu açmamış');
            return;
        }

        try {
            const userRef = doc(db, 'users', user.uid);
            const userDoc = await getDoc(userRef);

            if (userDoc.exists()) {
                const newAddress = {
                    id: Date.now().toString(),
                    bina_ve_daire: binaVeDaire,
                    mahalle,
                    sokak,
                    aciklama,
                    latitude: location.lat,
                    longitude: location.lng,
                    createdAt: new Date(),
                };

                await updateDoc(userRef, {
                    addresses: arrayUnion(newAddress),
                });

                router.push('/account/addresses');
            } else {
                console.error('Kullanıcı belgesi bulunamadı');
            }
        } catch (err) {
            console.error('Adres eklenirken hata oluştu:', err);
        }
    };

    if (loadError) {
        return <div>Harita yüklenirken bir hata oluştu: {loadError.message}</div>;
    }

    return (
        <div className="flex items-center justify-center py-10">
            <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
                <h2 className="text-2xl font-bold mb-6 text-center">Adres Ekle</h2>
                <AddressForm
                    sokak={sokak}
                    setSokak={setSokak}
                    mahalle={mahalle}
                    setMahalle={setMahalle}
                    binaVeDaire={binaVeDaire}
                    setBinaVeDaire={setBinaVeDaire}
                    aciklama={aciklama}
                    setAciklama={setAciklama}
                    handleAddAddress={handleAddAddress}
                />
                <div className="mb-4">
                    <label className="block text-gray-700 mb-2">Konum</label>
                    <label className="block text-gray-700 mb-2 text-sm">Harita üzerinde adresinize giderek dokunarak(tıklayın) işaretleyebilirsiniz</label>
                    <MapComponent
                        isLoaded={isLoaded}
                        location={location}
                        mapContainerStyle={mapContainerStyle}
                        handleMapClick={handleMapClick}
                        mapRef={mapRef}
                    />
                </div>
                <button
                    type="submit"
                    className="w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition-colors duration-300"
                    onClick={handleAddAddress}
                >
                    Ekle
                </button>
            </div>
        </div>
    );
}