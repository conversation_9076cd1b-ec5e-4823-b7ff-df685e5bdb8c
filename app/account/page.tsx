'use client';

import { Suspense, useMemo, useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { getAuth, updateProfile } from 'firebase/auth';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';

const AccountSectionPage = () => {
    const searchParams = useSearchParams();
    const section = useMemo(() => searchParams.get('section') || '', [searchParams]);
    const auth = getAuth();
    
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [phoneNumber, setPhoneNumber] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [message, setMessage] = useState('');

    useEffect(() => {
        const fetchUserData = async () => {
            if (!auth.currentUser) return;

            try {
                const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    setFirstName(userData.firstName || '');
                    setLastName(userData.lastName || '');
                    setPhoneNumber(userData.phoneNumber || auth.currentUser.phoneNumber || '');
                }
            } catch (error) {
                console.error('Kullanıcı bilgileri alınırken hata:', error);
            }
        };

        fetchUserData();
    }, [auth.currentUser]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!auth.currentUser) return;

        setIsLoading(true);
        try {
            // Firestore'da kullanıcı bilgilerini güncelle
            await updateDoc(doc(db, 'users', auth.currentUser.uid), {
                firstName,
                lastName,
                updatedAt: new Date()
            });

            // Auth profilini güncelle
            await updateProfile(auth.currentUser, {
                displayName: `${firstName} ${lastName}`
            });

            setMessage('Bilgileriniz başarıyla güncellendi');
            setTimeout(() => setMessage(''), 3000);
        } catch (error) {
            console.error('Güncelleme hatası:', error);
            setMessage('Bilgiler güncellenirken bir hata oluştu');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="space-y-6">
            <div className="border-b pb-4">
                <h1 className="text-2xl font-semibold text-gray-900 capitalize">
                    {section || 'Hesabım'}
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                    {section === 'addresses' && 'Kayıtlı adreslerinizi yönetin'}
                    {section === 'orders' && 'Siparişlerinizi takip edin'}
                    {!section && 'Hesap bilgilerinizi ve siparişlerinizi yönetin'}
                </p>
            </div>

            {!section && (
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                            Telefon Numarası
                        </label>
                        <input
                            type="tel"
                            id="phoneNumber"
                            value={phoneNumber}
                            className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm focus:border-blue-500 focus:ring-blue-500 cursor-not-allowed"
                            disabled
                            readOnly
                        />
                        <p className="mt-1 text-sm text-gray-500">
                            Telefon numarası değiştirilemez
                        </p>
                    </div>

                    <div>
                        <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                            Ad
                        </label>
                        <input
                            type="text"
                            id="firstName"
                            value={firstName}
                            onChange={(e) => setFirstName(e.target.value)}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            required
                        />
                    </div>

                    <div>
                        <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                            Soyad
                        </label>
                        <input
                            type="text"
                            id="lastName"
                            value={lastName}
                            onChange={(e) => setLastName(e.target.value)}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            required
                        />
                    </div>

                    <button
                        type="submit"
                        disabled={isLoading}
                        className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                        {isLoading ? 'Güncelleniyor...' : 'Bilgileri Güncelle'}
                    </button>

                    {message && (
                        <div className={`p-4 rounded-md ${message.includes('hata') ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
                            {message}
                        </div>
                    )}
                </form>
            )}
        </div>
    );
};

const AccountPageWrapper = () => (
    <Suspense fallback={
        <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
    }>
        <AccountSectionPage />
    </Suspense>
);

export default AccountPageWrapper;