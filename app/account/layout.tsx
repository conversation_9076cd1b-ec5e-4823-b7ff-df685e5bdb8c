import AccountNavbar from './Navbar';

const AccountLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return (
        <div className="min-h-screen bg-gray-50">
            <AccountNavbar />
            <div className="container mx-auto p-6 max-w-7xl">
                <main className="bg-white rounded-lg shadow-sm p-6">
                    {children}
                </main>
            </div>
        </div>
    );
};

export default AccountLayout;