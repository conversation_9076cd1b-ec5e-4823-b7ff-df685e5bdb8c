import React from 'react';

interface AddressFormProps {
    sokak: string;
    setSokak: (value: string) => void;
    mahalle: string;
    setMahalle: (value: string) => void;
    binaVeDaire: string;
    setBinaVeDaire: (value: string) => void;
    aciklama: string;
    setAciklama: (value: string) => void;
    handleAddAddress: (e: React.FormEvent) => void;
}

const AddressForm: React.FC<AddressFormProps> = ({
                                                     sokak,
                                                     setSokak,
                                                     mahalle,
                                                     setMahalle,
                                                     binaVeDaire,
                                                     setBinaVeDaire,
                                                     aciklama,
                                                     setAciklama,
                                                     handleAddAddress
                                                 }) => {
    return (
        <form onSubmit={handleAddAddress}>
            <div className="mb-4">
                <label className="block text-gray-700 mb-2">Sokak</label>
                <input
                    type="text"
                    value={sokak}
                    onChange={(e) => setSokak(e.target.value)}
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                    required
                />
            </div>
            <div className="mb-4">
                <label className="block text-gray-700 mb-2">Mahalle</label>
                <input
                    type="text"
                    value={mahalle}
                    onChange={(e) => setMahalle(e.target.value)}
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                    required
                />
            </div>
            <div className="mb-4">
                <label className="block text-gray-700 mb-2">Bina ve Daire</label>
                <input
                    type="text"
                    value={binaVeDaire}
                    onChange={(e) => setBinaVeDaire(e.target.value)}
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                    required
                />
            </div>
            <div className="mb-4">
                <label className="block text-gray-700 mb-2">Açıklama</label>
                <textarea
                    value={aciklama}
                    onChange={(e) => setAciklama(e.target.value)}
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                />
            </div>

        </form>
    );
};

export default AddressForm;