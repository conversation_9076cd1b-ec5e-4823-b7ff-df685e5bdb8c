import React from 'react';
import { GoogleMap } from '@react-google-maps/api';

interface MapComponentProps {
    isLoaded: boolean;
    location: { lat: number; lng: number };
    mapContainerStyle: { width: string; height: string };
    handleMapClick: (event: google.maps.MapMouseEvent) => void;
    mapRef: React.MutableRefObject<google.maps.Map | null>;
}

const MapComponent: React.FC<MapComponentProps> = ({
                                                       isLoaded,
                                                       location,
                                                       mapContainerStyle,
                                                       handleMapClick,
                                                       mapRef
                                                   }) => {
    return (
        <>
            {isLoaded && (
                <GoogleMap
                    mapContainerStyle={mapContainerStyle}
                    center={location}
                    zoom={15}
                    onClick={handleMapClick}
                    onLoad={(map) => { mapRef.current = map; }}
                />
            )}
        </>
    );
};

export default MapComponent;