'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { getAuth, signOut } from 'firebase/auth';
import { MapPin, ShoppingBag, LogOut, User } from 'lucide-react';

const Navbar = () => {
    const router = useRouter();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const auth = getAuth();

    const handleLogout = () => {
        setIsModalOpen(true);
    };

    const confirmLogout = async () => {
        try {
            await signOut(auth);
            setIsModalOpen(false);
            if (window.grecaptcha) {
                window.grecaptcha.reset();
            }
            router.push('/signin');
            window.location.reload();
        } catch (error) {
            console.error('Error logging out: ', error);
        }
    };

    return (
        <nav className="bg-white border-b">
            <div className="container mx-auto px-4">
                <div className="h-16 flex items-center justify-between">
                    {/* Sol taraf - Navigasyon linkleri */}
                    <div className="flex items-center space-x-6">
                        <Link
                            href="/account"
                            className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                        >
                            <User className="w-5 h-5" />
                            <span>Hesabım</span>
                        </Link>
                        <Link
                            href="/account/addresses"
                            className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                        >
                            <MapPin className="w-5 h-5" />
                            <span>Adreslerim</span>
                        </Link>
                        <Link
                            href="/account/orders"
                            className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                        >
                            <ShoppingBag className="w-5 h-5" />
                            <span>Siparişlerim</span>
                        </Link>
                    </div>

                    {/* Sağ taraf - Çıkış butonu */}
                    <button
                        onClick={handleLogout}
                        className="flex items-center space-x-2 text-red-600 hover:text-red-700 transition-colors"
                    >
                        <LogOut className="w-5 h-5" />
                        <span>Çıkış Yap</span>
                    </button>
                </div>
            </div>

            {/* Modal */}
            {isModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <h2 className="text-xl font-semibold mb-4">
                            Çıkış yapmak istediğinizden emin misiniz?
                        </h2>
                        <p className="text-gray-600 mb-6">
                            Oturumunuz sonlandırılacak ve yeniden giriş yapmanız gerekecek.
                        </p>
                        <div className="flex justify-end space-x-4">
                            <button
                                onClick={() => setIsModalOpen(false)}
                                className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
                            >
                                İptal
                            </button>
                            <button
                                onClick={confirmLogout}
                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 font-medium"
                            >
                                Çıkış Yap
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </nav>
    );
};

export default Navbar;