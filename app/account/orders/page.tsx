'use client';
import React, { useEffect, useState } from 'react';
import { getAuth } from 'firebase/auth';
import { db } from '@/config/firebaseConfig';
import { collection, doc, getDocs, updateDoc } from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Timestamp } from 'firebase/firestore';
import { Star } from 'lucide-react';

interface OrderItem {
    id: string;
    productName: string;
    price: number;
    quantity: number;
    imageURL: string;
    firmName?: string;
    firmId?: string;
    categoryName?: string;
    itemStatus?: string;
    selectedFeatures?: {
        name: string;
        price: string | number;
    }[];
    review?: {
        rating: number;
        comment: string;
    };
}

interface Order {
    id: string;
    items: OrderItem[];
    totalPrice: number;
    finalPrice: number;
    discountApplied?: number;
    discountType?: string;
    appliedCoupon?: {
        code: string;
        discount: number;
        id: string;
    };
    isFirstOrder?: boolean;
    deliveryInstructions: string[];
    paymentMethod: string;
    instructions: string;
    orderDate: Timestamp;
    status: string;
    addresses: string[];
}

export default function MyOrdersPage() {
    const [orders, setOrders] = useState<Order[]>([]);
    const [selectedItem, setSelectedItem] = useState<OrderItem | null>(null);
    const [rating, setRating] = useState(0);
    const [comment, setComment] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showReviewModal, setShowReviewModal] = useState(false);
    const auth = getAuth();
    const router = useRouter();

    useEffect(() => {
        fetchOrders();
    }, []);

    const fetchOrders = async () => {
        const user = auth.currentUser;
        if (!user) {
            router.push('/signin');
            return;
        }

        const ordersRef = collection(db, 'users', user.uid, 'orders');
        const ordersSnapshot = await getDocs(ordersRef);
        const ordersData = ordersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Order[];
        setOrders(ordersData.sort((a, b) => b.orderDate.toMillis() - a.orderDate.toMillis()));
    };

    const submitReview = async () => {
        if (!selectedItem || !auth.currentUser) return;
        setIsSubmitting(true);

        const review = { rating, comment };
        const userId = auth.currentUser.uid;

        try {
            // Update user's order item
            const order = orders.find(order => order.items.some(item => item.id === selectedItem.id));
            if (order) {
                const updatedItems = order.items.map(item =>
                    item.id === selectedItem.id ? { ...item, review } : item
                );
                await updateDoc(doc(db, 'users', userId, 'orders', order.id), { items: updatedItems });

                // Update order item in orders collection
                await updateDoc(doc(db, 'orders', order.id), { items: updatedItems });

                // Update order item in firm's collection
                const firmId = selectedItem.firmId;
                if (firmId) {
                    await updateDoc(doc(db, 'firms', firmId, 'orders', order.id), { items: updatedItems });
                }

                setOrders(orders.map(o =>
                    o.id === order.id ? { ...o, items: updatedItems } : o
                ));
            }
            setShowReviewModal(false);
            setSelectedItem(null);
            setRating(0);
            setComment('');
        } catch (error) {
            console.error('Error submitting review:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const groupedOrders = orders.reduce((acc, order) => {
        if (!acc[order.id]) acc[order.id] = [];
        acc[order.id].push(order);
        return acc;
    }, {} as Record<string, Order[]>);

    const renderStars = (count: number, onStarClick?: (index: number) => void) => (
        <div className="flex space-x-1">
            {[1, 2, 3, 4, 5].map((index) => (
                <Star
                    key={index}
                    className={`w-5 h-5 ${index <= count ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}
                        ${onStarClick ? 'cursor-pointer' : ''}`}
                    onClick={() => onStarClick?.(index)}
                />
            ))}
        </div>
    );

    return (
        <div className="container mx-auto py-10 px-4">
            <h1 className="text-3xl font-bold mb-8">Siparişlerim</h1>

            {Object.entries(groupedOrders).map(([orderId, orderGroup]) => (
                <div key={orderId} className="mb-10">
                    <h2 className="text-2xl font-semibold mb-4">Sipariş ID: {orderId}</h2>
                    <div className="grid gap-6">
                        {orderGroup.map(order => (
                            <div key={order.id} className="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
                                <div className="flex justify-between items-start mb-4">
                                    <div>
                                        <div className="flex items-center gap-3 mb-2">
                                            <div>
                                                <p className="text-lg font-bold text-orange-600">{order.finalPrice || order.totalPrice} TL</p>
                                                {(order.appliedCoupon || (order.isFirstOrder && order.discountApplied)) && (
                                                    <div className="text-sm text-gray-600">
                                                        <span className="line-through">{order.totalPrice} TL</span>
                                                        {order.appliedCoupon && (
                                                            <span className="text-green-600 ml-2">
                                                                ({order.appliedCoupon.code} kuponu)
                                                            </span>
                                                        )}
                                                        {order.isFirstOrder && !order.appliedCoupon && (
                                                            <span className="text-green-600 ml-2">
                                                                (İlk sipariş indirimi)
                                                            </span>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                            <span className={`inline-block px-3 py-1 text-sm rounded-full
                                                ${order.status === "Teslim Edildi"
                                                    ? "bg-green-100 text-green-800"
                                                    : order.status === "İptal Edildi"
                                                    ? "bg-red-100 text-red-800"
                                                    : "bg-blue-100 text-blue-800"}`}
                                            >
                                                {order.status}
                                            </span>
                                        </div>
                                        <p className="text-sm text-gray-600">{order.paymentMethod}</p>
                                        <p className="text-sm text-gray-600">
                                            {new Date(order.orderDate.toMillis()).toLocaleDateString('tr-TR')}
                                        </p>
                                    </div>
                                </div>

                                <div className="grid gap-4">
                                    {order.items.map(item => (
                                        <div key={item.id} className="flex gap-4 p-4 bg-gray-50 rounded-lg">
                                            <Image
                                                src={item.imageURL}
                                                alt={item.productName}
                                                width={80}
                                                height={80}
                                                className="rounded-lg object-cover"
                                            />
                                            <div className="flex-1">
                                                <h4 className="font-semibold">{item.productName}</h4>
                                                <p className="text-sm text-gray-600">{item.price} TL x {item.quantity}</p>
                                                <p className="text-sm text-gray-500">{item.firmName}</p>
                                                <p className="text-sm text-gray-500">{item.categoryName}</p>
                                                
                                                {item.selectedFeatures && item.selectedFeatures.length > 0 && (
                                                    <div className="mt-2">
                                                        <p className="text-sm font-medium text-gray-700">Ek Seçimler:</p>
                                                        <ul className="text-sm text-gray-600">
                                                            {item.selectedFeatures.map((feature, index) => (
                                                                <li key={index} className="flex justify-between items-center">
                                                                    <span>{feature.name}</span>
                                                                    <span>{feature.price} TL</span>
                                                                </li>
                                                            ))}
                                                        </ul>
                                                    </div>
                                                )}

                                                {item.review ? (
                                                    <div className="mt-2">
                                                        <div className="flex items-center gap-2 mb-2">
                                                            {renderStars(item.review.rating)}
                                                        </div>
                                                        <p className="text-gray-700">{item.review.comment}</p>
                                                    </div>
                                                ) : (
                                                    <>
                                                        {order.status === "Teslim Edildi" ? (
                                                            <button
                                                                onClick={() => {
                                                                    setSelectedItem(item);
                                                                    setShowReviewModal(true);
                                                                }}
                                                                className="mt-2 bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-blue-600 transition"
                                                            >
                                                                <Star className="w-4 h-4" />
                                                                <span>Değerlendir</span>
                                                            </button>
                                                        ) : (
                                                            <p className="mt-2 text-sm text-gray-500 italic">
                                                                Değerlendirme için siparişin teslim edilmesi gerekiyor
                                                            </p>
                                                        )}
                                                    </>
                                                )}
                                            </div>
                                            <div className="text-right">
                                                <span className={`inline-block px-3 py-1 text-sm rounded-full 
                                                    ${order.status === "Teslim Edildi" 
                                                        ? "bg-green-100 text-green-800"
                                                        : order.status === "İptal Edildi"
                                                        ? "bg-red-100 text-red-800"
                                                        : "bg-blue-100 text-blue-800"}`}
                                                >
                                                    {order.status}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            ))}

            {showReviewModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <h3 className="text-xl font-bold mb-4">Ürünü Değerlendir</h3>
                        <div className="flex flex-col gap-4">
                            <div className="flex justify-center mb-4">
                                {renderStars(rating, setRating)}
                            </div>
                            <textarea
                                placeholder="Yorumunuzu yazın..."
                                value={comment}
                                onChange={(e) => setComment(e.target.value)}
                                className="w-full p-2 border rounded-lg min-h-[100px]"
                            />
                            <div className="flex gap-2 justify-end">
                                <button
                                    onClick={() => setShowReviewModal(false)}
                                    className="px-4 py-2 rounded-lg border hover:bg-gray-100 transition"
                                >
                                    İptal
                                </button>
                                <button
                                    onClick={submitReview}
                                    disabled={isSubmitting || rating === 0 || !comment.trim()}
                                    className="px-4 py-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition disabled:opacity-50"
                                >
                                    {isSubmitting ? 'Gönderiliyor...' : 'Değerlendir'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}