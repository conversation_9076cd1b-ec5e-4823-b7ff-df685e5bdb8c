'use client';

import React, {useEffect, useState, useRef} from 'react';
import {useParams, useRouter} from 'next/navigation';
import {db} from '@/config/firebaseConfig';
import {
    collection,
    doc,
    getDoc,
    getDocs,
    query,
    where,
    setDoc,
    addDoc,
    serverTimestamp,
    arrayUnion,
    arrayRemove,
    updateDoc
} from 'firebase/firestore';
import {getAuth} from 'firebase/auth';
import {FaStar, FaStarHalfAlt} from 'react-icons/fa';
import FirmReviews from '@/app/firms/components/FirmReviews';
import BannerSection from "@/app/firms/components/BannerSection";
import CategoryNav from "@/app/firms/components/CategoryNav";
import ProductCard from "@/app/firms/components/ProductCard";

interface Firm {
    id: string;
    name: string;
    description: string;
    banner: string;
    categories: string[];
    visible: boolean;
}

interface Product {
    id: string;
    name: string;
    description: string;
    images: string[];
    category: string;
    price: string;
    stockStatus: string;
    order: number;
}

export default function FirmDetailPage() {
    const params = useParams();
    const firmId = typeof params.firmId === 'string' ? params.firmId : '';
    const [firm, setFirm] = useState<Firm | null>(null);
    const [products, setProducts] = useState<Product[]>([]);
    const [categories, setCategories] = useState<{ [key: string]: string }>({});
    const [message, setMessage] = useState<{ [key: string]: string }>({});
    const [activeCategory, setActiveCategory] = useState<string>('');
    const [isFavorite, setIsFavorite] = useState(false);
    const [averageRating, setAverageRating] = useState<number>(0);
    const [totalReviews, setTotalReviews] = useState<number>(0);
    const reviewsRef = useRef<HTMLDivElement>(null);
    const router = useRouter();
    const auth = getAuth();
    const categoryRefs = useRef<{ [key: string]: HTMLDivElement }>({});

    // Firma detaylarını çekme
    useEffect(() => {
        const fetchFirmDetails = async () => {
            if (!firmId) return;

            try {
                const firmDoc = await getDoc(doc(db, 'firms', firmId));
                if (firmDoc.exists()) {
                    setFirm({id: firmDoc.id, ...firmDoc.data()} as Firm);
                }

                let productsData: Product[] = [];
                if (firmDoc.data()?.visible) {
                    const productsQuery = query(
                        collection(db, 'products'),
                        where('firmId', '==', firmId)
                    );
                    const productsSnapshot = await getDocs(productsQuery);
                    productsData = productsSnapshot.docs
                        .map(doc => ({id: doc.id, ...doc.data()} as Product))
                        .filter(product => product.stockStatus === "Stokta Var")
                        .sort((a, b) => (a.order || 0) - (b.order || 0));
                    setProducts(productsData);
                }

                // Get unique category IDs from the products
                const uniqueCategoryIds = [...new Set(productsData.map(product => product.category))];

                // Only fetch categories that exist in products
                const categoriesSnapshot = await getDocs(collection(db, 'categories'));
                const categoriesArray = categoriesSnapshot.docs
                    .filter(doc => uniqueCategoryIds.includes(doc.id))
                    .map(doc => ({id: doc.id, name: doc.data().name}));

                // Apply the same sorting logic for SOĞUK İÇECEKLER
                const filteredCategoriesArray = categoriesArray
                    .filter(category => category.name !== "SOĞUK İÇECEKLER");
                const sogukIceceklerCategory = categoriesArray
                    .find(category => category.name === "SOĞUK İÇECEKLER");
                if (sogukIceceklerCategory) {
                    filteredCategoriesArray.push(sogukIceceklerCategory);
                }

                const reorderedCategories = filteredCategoriesArray.reduce((acc, category) => {
                    acc[category.id] = category.name;
                    return acc;
                }, {} as { [key: string]: string });

                setCategories(reorderedCategories);

                if (filteredCategoriesArray.length > 0) {
                    setActiveCategory(filteredCategoriesArray[0].id);
                }
            } catch (error) {
                console.error('Error fetching firm details:', error);
            }
        };

        fetchFirmDetails();
    }, [firmId]);

    // Yorum verilerini çekme
    useEffect(() => {
        const fetchReviews = async () => {
            if (!firmId) return;

            try {
                const ordersRef = collection(db, 'orders');
                const ordersSnapshot = await getDocs(ordersRef);
                const fetchedReviews: { rating: number }[] = [];

                for (const orderDoc of ordersSnapshot.docs) {
                    const orderData = orderDoc.data() as {
                        items: { firmId: string; review?: { rating: number; comment: string } }[]
                    };
                    const relevantItems = orderData.items?.filter(item =>
                        item.firmId === firmId &&
                        item.review?.rating &&
                        item.review?.comment
                    );

                    relevantItems?.forEach(item => {
                        if (item.review) {
                            fetchedReviews.push(item.review);
                        }
                    });
                }

                setTotalReviews(fetchedReviews.length);

                if (fetchedReviews.length > 0) {
                    const avgRating = fetchedReviews.reduce((acc, review) => acc + review.rating, 0) / fetchedReviews.length;
                    setAverageRating(Number(avgRating.toFixed(1)));
                }
            } catch (error) {
                console.error('Error fetching reviews:', error);
            }
        };

        fetchReviews();
    }, [firmId]);

    // Favori durumunu kontrol etme
    useEffect(() => {
        const checkFavoriteStatus = async () => {
            if (!auth.currentUser || !firmId) return;

            try {
                const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    const favorites = userData.favorites || [];
                    setIsFavorite(favorites.includes(firmId));
                }
            } catch (error) {
                console.error('Favori durumu kontrol edilirken hata:', error);
            }
        };

        checkFavoriteStatus();
    }, [auth.currentUser, firmId]);

    const renderStars = (rating: number) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;

        for (let i = 0; i < fullStars; i++) {
            stars.push(<FaStar key={`star-${i}`} className="text-yellow-400"/>);
        }

        if (hasHalfStar) {
            stars.push(<FaStarHalfAlt key="half-star" className="text-yellow-400"/>);
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(<FaStar key={`empty-star-${i}`} className="text-gray-300"/>);
        }

        return stars;
    };

    const scrollToReviews = () => {
        reviewsRef.current?.scrollIntoView({behavior: 'smooth', block: 'start'});
    };

    const toggleFavorite = async () => {
        if (!auth.currentUser || !firmId) {
            router.push('/signin');
            return;
        }

        try {
            const userRef = doc(db, 'users', auth.currentUser.uid);

            if (isFavorite) {
                await updateDoc(userRef, {
                    favorites: arrayRemove(firmId)
                });
                setMessage(prev => ({...prev, 'favorite': 'Favorilerden kaldırıldı!'}));
            } else {
                await updateDoc(userRef, {
                    favorites: arrayUnion(firmId)
                });
                setMessage(prev => ({...prev, 'favorite': 'Favorilere eklendi!'}));
            }

            setIsFavorite(!isFavorite);
            setTimeout(() => setMessage(prev => ({...prev, 'favorite': ''})), 3000);
        } catch (error) {
            console.error('Favori işlemi sırasında hata:', error);
        }
    };

    const handleAddToCart = async (product: Product) => {
        if (!firm?.visible) {
            setMessage(prev => ({...prev, [product.id]: 'İşletme şu anda kapalı!'}));
            setTimeout(() => setMessage(prev => ({...prev, [product.id]: ''})), 3000);
            return;
        }

        const user = auth.currentUser;
        if (!user) {
            router.push('/signin');
            return;
        }

        try {
            const cartItem = {
                addedAt: serverTimestamp(),
                categoryId: product.category,
                firmId: firmId,
                imageURL: product.images[0],
                price: parseFloat(product.price),
                productId: product.id,
                productName: product.name,
                quantity: 1,
                selectedFeatures: [],
                status: 'sipariş alındı'
            };

            const cartRef = doc(db, 'users', user.uid, 'activeCartWeb', 'cart');
            await setDoc(cartRef, {updatedAt: serverTimestamp()}, {merge: true});
            await addDoc(collection(cartRef, 'items'), cartItem);

            setMessage(prev => ({...prev, [product.id]: 'Sepete eklendi!'}));
            setTimeout(() => setMessage(prev => ({...prev, [product.id]: ''})), 3000);
        } catch (err) {
            console.error('Ürün sepete eklenirken hata oluştu:', err);
        }
    };

    const scrollToCategory = (categoryId: string) => {
        setActiveCategory(categoryId);
        const element = categoryRefs.current[categoryId];
        if (element) {
            const headerOffset = 100;
            const elementPosition = element.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    };

    const setRef = (categoryId: string) => (el: HTMLDivElement | null) => {
        if (el) {
            categoryRefs.current[categoryId] = el;
        }
    };

    if (!firm) {
        return <p>Yükleniyor...</p>;
    }

    const productsByCategory = products.reduce((acc, product) => {
        if (!acc[product.category]) {
            acc[product.category] = [];
        }
        acc[product.category].push(product);
        acc[product.category].sort((a, b) => (a.order || 0) - (b.order || 0));
        return acc;
    }, {} as { [key: string]: Product[] });

    const reorderedProductsByCategory = Object.keys(productsByCategory).sort((a, b) => {
        if (categories[a] === "SOĞUK İÇECEKLER") return 1;
        if (categories[b] === "SOĞUK İÇECEKLER") return -1;
        return 0;
    });

    return (
        <div className="container mx-auto py-10">
            <BannerSection
                firm={firm}
                isFavorite={isFavorite}
                averageRating={averageRating}
                totalReviews={totalReviews}
                toggleFavorite={toggleFavorite}
                scrollToReviews={scrollToReviews}
                renderStars={renderStars}
            />

            {!firm.visible && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                    <p className="font-medium">
                        Bu işletme şu anda kapalıdır. Siparişler geçici olarak alınmamaktadır.
                    </p>
                </div>
            )}

            <CategoryNav
                categories={categories}
                activeCategory={activeCategory}
                scrollToCategory={scrollToCategory}
            />

            {reorderedProductsByCategory.map(categoryId => (
                <div
                    key={categoryId}
                    className="mb-10"
                    ref={setRef(categoryId)}
                >
                    <h3 className="text-2xl font-semibold mb-4 p-2">{categories[categoryId]}</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        {productsByCategory[categoryId].map(product => (
                            <ProductCard
                                key={product.id}
                                product={product}
                                firm={firm}
                                handleAddToCart={handleAddToCart}
                                message={message}
                            />
                        ))}
                    </div>
                </div>
            ))}

            <div ref={reviewsRef} className="mb-10 scroll-mt-20">
                <FirmReviews firmId={firmId}/>
            </div>

            {/* Notification Messages */}
            {Object.entries(message).map(([id, msg]) => msg && (
                <div
                    key={id}
                    className={`fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 
                        ${msg.includes('kapalı') ? 'bg-red-500' : 'bg-green-500'} text-white`}
                >
                    {msg}
                </div>
            ))}
        </div>
    );
}