import React from 'react';
import { Star } from 'lucide-react';
import { db } from '@/config/firebaseConfig';
import { collection, getDocs } from 'firebase/firestore';

interface Review {
    rating: number;
    comment: string;
}

interface OrderItem {
    id: string;
    firmId: string;
    productName: string;
    review?: Review;
}

interface Order {
    id: string;
    items: OrderItem[];
    firstName: string;
}

interface ReviewWithUser extends Review {
    firstName: string;
    productName: string;
}

interface FirmReviewsProps {
    firmId: string;
}

function maskName(name: string): string {
    if (!name || name === 'Anonim') return 'Anonim';
    
    const words = name.split(' ');
    return words.map(word => {
        if (word.length <= 1) return word;
        return word[0] + '*'.repeat(word.length - 1);
    }).join(' ');
}

export default function FirmReviews({ firmId }: FirmReviewsProps) {
    const [reviews, setReviews] = React.useState<ReviewWithUser[]>([]);
    const [averageRating, setAverageRating] = React.useState<number>(0);
    const [totalReviews, setTotalReviews] = React.useState<number>(0);
    const [isLoading, setIsLoading] = React.useState(true);

    React.useEffect(() => {
        const fetchReviews = async () => {
            if (!firmId) return;

            try {
                // First, get all orders for this firm
                const ordersRef = collection(db, 'orders');
                const ordersSnapshot = await getDocs(ordersRef);

                const fetchedReviews: ReviewWithUser[] = [];

                for (const orderDoc of ordersSnapshot.docs) {
                    const orderData = orderDoc.data() as Order;

                    // Find items in this order that belong to our firm and have reviews
                    const relevantItems = orderData.items.filter(item =>
                        item.firmId === firmId &&
                        item.review?.rating &&
                        item.review?.comment
                    );

                    // Add reviews from relevant items
                    relevantItems.forEach(item => {
                        if (item.review) {
                            fetchedReviews.push({
                                ...item.review,
                                firstName: orderData.firstName || 'Anonim',
                                productName: item.productName || 'Ürün adı bulunamadı'
                            });
                        }
                    });
                }

                setReviews(fetchedReviews);
                setTotalReviews(fetchedReviews.length);

                if (fetchedReviews.length > 0) {
                    const avgRating = fetchedReviews.reduce((acc, review) => acc + review.rating, 0) / fetchedReviews.length;
                    setAverageRating(avgRating);
                }
            } catch (error) {
                console.error('Error fetching reviews:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchReviews();
    }, [firmId]);

    const renderStars = (rating: number) => (
        <div className="flex">
            {[1, 2, 3, 4, 5].map((star) => (
                <Star
                    key={star}
                    className={`w-5 h-5 ${
                        star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                    }`}
                />
            ))}
        </div>
    );

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-2xl font-semibold mb-6">Müşteri Değerlendirmeleri</h3>

            {totalReviews > 0 && (
                <div className="flex flex-col items-center mb-6 pb-6 border-b">
                    <div className="text-4xl font-bold text-orange-600 mb-2">
                        {averageRating.toFixed(1)}
                    </div>
                    <div className="flex items-center gap-2">
                        {renderStars(averageRating)}
                        <span className="text-sm text-gray-500">({totalReviews} değerlendirme)</span>
                    </div>
                </div>
            )}

            {reviews.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                    Henüz değerlendirme yapılmamış
                </div>
            ) : (
                <div className="space-y-4">
                    {reviews.map((review, index) => (
                        <div key={index} className="p-4 bg-gray-50 rounded-lg">
                            <div className="flex justify-between items-center mb-2">
                                <div className="flex items-center gap-2">
                                    {renderStars(review.rating)}
                                </div>
                                <span className="text-sm font-medium text-gray-600">
                                    {maskName(review.firstName)}
                                </span>
                            </div>
                            <p className="text-gray-700 font-semibold">{review.productName}</p>
                            <p className="text-gray-700">{review.comment}</p>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}