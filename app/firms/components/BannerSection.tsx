import React from 'react';
import { FaHeart, FaComment } from 'react-icons/fa';
import Image from 'next/image';

interface Firm {
    id: string;
    name: string;
    banner: string;
    visible: boolean;
}

interface BannerSectionProps {
    firm: Firm;
    isFavorite: boolean;
    averageRating: number;
    totalReviews: number;
    toggleFavorite: () => void;
    scrollToReviews: () => void;
    renderStars: (rating: number) => JSX.Element[];
}

const BannerSection: React.FC<BannerSectionProps> = ({
                                                         firm,
                                                         isFavorite,
                                                         averageRating,
                                                         totalReviews,
                                                         toggleFavorite,
                                                         scrollToReviews,
                                                         renderStars
                                                     }) => {
    return (
        <div className="w-full bg-gray-900">
            <div className="container mx-auto">
                <div className="flex flex-col lg:flex-row gap-8 p-6">
                    {/* Left side - Image Container */}
                    <div className="relative w-full lg:w-1/3 aspect-square rounded-2xl overflow-hidden">
                        <Image
                            src={firm.banner}
                            alt={`${firm.name} banner`}
                            layout="fill"
                            objectFit="cover"
                            priority
                            className={`
                                transition-all duration-700
                                hover:scale-105
                                ${!firm.visible ? "filter grayscale" : ""}
                            `}
                        />

                        {/* Favorite Button - Repositioned */}
                        <button
                            onClick={toggleFavorite}
                            aria-label={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                            className={`
                                absolute top-4 right-4 p-3 rounded-xl
                                transition-all duration-300 backdrop-blur-md
                                shadow-xl hover:shadow-2xl
                                transform hover:scale-105
                                ${
                                isFavorite
                                    ? 'bg-red-500/30 text-red-500 border-red-500/40 hover:bg-red-500/40'
                                    : 'bg-black/30 text-white/90 border-white/10 hover:bg-black/40'
                            }
                            `}
                        >
                            <FaHeart
                                className={`
                                    text-xl transition-all duration-300
                                    ${isFavorite ? 'scale-110' : 'scale-100'}
                                    hover:animate-pulse
                                `}
                            />
                        </button>
                    </div>

                    {/* Right side - Content Container */}
                    <div className="flex flex-col justify-center gap-6 lg:w-2/3">
                        {/* Firm Name */}
                        <div>
                            <h1 className="text-4xl lg:text-5xl font-black tracking-tight text-white
                                mb-4">
                                {firm.name}
                            </h1>

                            {/* Status Indicator - Repositioned */}
                            {!firm.visible && (
                                <div className="inline-block bg-red-500/80 backdrop-blur-md px-4 py-2 rounded-lg
                                    text-sm font-medium text-white border border-red-400/20
                                    shadow-lg animate-pulse">
                                    Şu anda kapalı
                                </div>
                            )}
                        </div>

                        {/* Rating and Reviews Section */}
                        <div className="flex flex-wrap items-center gap-4">
                            {/* Rating Display */}
                            <div className="flex items-center gap-3 bg-black/40 backdrop-blur-md
                                px-4 py-2 rounded-xl border border-white/10
                                shadow-lg transition-shadow duration-300">
                                <div className="flex gap-1">
                                    {renderStars(averageRating)}
                                </div>
                                <div className="w-px h-5 bg-white/20"/>
                                <span className="text-white font-semibold">
                                    {averageRating.toFixed(1)} ({totalReviews})
                                </span>
                            </div>

                            {/* Reviews Button */}
                            <button
                                onClick={scrollToReviews}
                                className="flex items-center gap-2 bg-orange-500 hover:bg-orange-600
                                    text-white px-5 py-2 rounded-xl transition-all duration-300
                                    shadow-lg hover:shadow-xl hover:shadow-orange-500/25
                                    transform hover:-translate-y-0.5"
                            >
                                <FaComment className="text-lg"/>
                                <span className="font-medium">Yorumları Gör</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BannerSection;