import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaPlus } from 'react-icons/fa';

interface Product {
    id: string;
    name: string;
    description: string;
    images: string[];
    category: string;
    price: string;
    stockStatus: string;
    order: number;
}

interface Firm {
    id: string;
    visible: boolean;
}

interface ProductCardProps {
    product: Product;
    firm: Firm;
    handleAddToCart: (product: Product) => void;
    message: { [key: string]: string };
}

const ProductCard: React.FC<ProductCardProps> = ({ product, firm, handleAddToCart, message }) => {
    return (
        <div className={`border border-gray-200 p-4 rounded-xl shadow-lg hover:shadow-xl 
                    transition-all duration-300 bg-white ${!firm.visible ? 'opacity-75' : ''}`}>
            <Link href={`/products/${product.id}`}>
                <div className="group relative w-full pt-[100%] mb-4 overflow-hidden rounded-xl">
                    <Image
                        src={product.images[0]}
                        alt={product.name}
                        layout="fill"
                        objectFit="contain"
                        className={`rounded-xl transition-transform duration-300 group-hover:scale-105
                       ${!firm.visible ? 'filter grayscale' : ''}`}
                    />
                </div>

                <h4 className="text-xl font-bold mb-2 text-gray-800">{product.name}</h4>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
                <p className="text-lg font-semibold text-orange-600">{product.price} ₺</p>
            </Link>

            <div className="mt-4">
                <button
                    onClick={() => handleAddToCart(product)}
                    className={`w-full px-4 py-3 rounded-xl flex items-center justify-center gap-2
                     transition-all duration-300 font-medium ${
                        firm.visible
                            ? 'bg-orange-600 text-white hover:bg-orange-700 shadow-lg hover:shadow-orange-500/25'
                            : 'bg-gray-400 text-gray-200 cursor-not-allowed'
                    }`}
                    disabled={!firm.visible}
                >
                    <FaPlus className="text-sm" />
                    <span>Sepete Ekle</span>
                </button>
            </div>

            {message[product.id] && (
                <div className={`mt-3 text-center text-sm font-medium animate-fade-in ${
                    message[product.id].includes('kapalı')
                        ? 'text-red-500'
                        : 'text-green-500'
                }`}>
                    {message[product.id]}
                </div>
            )}
        </div>
    );
};

export default ProductCard;