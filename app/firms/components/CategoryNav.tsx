import React from 'react';

interface CategoryNavProps {
    categories: { [key: string]: string };
    activeCategory: string;
    scrollToCategory: (id: string) => void;
}

const CategoryNav: React.FC<CategoryNavProps> = ({ categories, activeCategory, scrollToCategory }) => {
    return (
        <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-md shadow-lg mb-6">
            <div className="container mx-auto px-4">
                <div className="flex overflow-x-auto py-4 gap-4 scrollbar-hide">
                    {Object.entries(categories).map(([id, name]) => (
                        <button
                            key={id}
                            onClick={() => scrollToCategory(id)}
                            className={`px-6 py-3 rounded-xl whitespace-nowrap transition-all duration-300
                         font-medium shadow-sm hover:shadow-md ${
                                activeCategory === id
                                    ? 'bg-orange-600 text-white shadow-orange-500/20'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                        >
                            {name as React.ReactNode}
                        </button>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default CategoryNav;