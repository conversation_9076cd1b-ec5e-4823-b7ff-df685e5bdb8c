'use client';
import React, {useState, useEffect} from 'react';
import {useFirestoreData} from '../../hooks/useFirestoreData';
import {doc, getDoc} from 'firebase/firestore';
import {db} from '@/config/firebaseConfig';
import FirmCard from '../components/All-firms/FirmCard';
import {FaSearch, FaStore, FaSpinner, FaFilter, FaClock, FaBan} from 'react-icons/fa';

interface Category {
    id: string;
    name: string;
}

export default function AllFirmsPage() {
    const {firms, loading} = useFirestoreData();
    const [searchTerm, setSearchTerm] = useState('');
    const [showOnlyOpen, setShowOnlyOpen] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [categories, setCategories] = useState<Category[]>([]);
    const [loadingCategories, setLoadingCategories] = useState(true);

    useEffect(() => {
        const fetchCategories = async () => {
            const uniqueCategories = new Set<string>();
            firms.forEach(firm => {
                firm.categories?.forEach(category => uniqueCategories.add(category));
            });

            try {
                const categoriesData: Category[] = [];
                for (const categoryId of uniqueCategories) {
                    const categoryDoc = await getDoc(doc(db, 'categories', categoryId));
                    if (categoryDoc.exists()) {
                        categoriesData.push({
                            id: categoryDoc.id,
                            name: categoryDoc.data().name
                        });
                    }
                }

                const sortedCategories = categoriesData.sort((a, b) =>
                    a.name.localeCompare(b.name, 'tr')
                );

                setCategories(sortedCategories);
            } catch (error) {
                console.error('Kategoriler yüklenirken hata:', error);
            } finally {
                setLoadingCategories(false);
            }
        };

        if (firms.length > 0) {
            fetchCategories();
        }
    }, [firms]);

    // Firmaları filtrele
    const filteredFirms = firms.filter(firm => {
        const matchesSearch = firm.name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || firm.categories?.includes(selectedCategory);
        const matchesStatus = !showOnlyOpen || firm.visible;
        return matchesSearch && matchesCategory && matchesStatus;
    }).sort((a, b) => (a.visible === b.visible ? 0 : a.visible ? -1 : 1));

    const activeCount = firms.filter(firm => firm.visible).length;

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <FaSpinner className="animate-spin text-4xl text-orange-600 mx-auto mb-4"/>
                    <p className="text-gray-600">Restoranlar yükleniyor...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header ve Özet Bilgiler */}
            <div className="bg-white border-b">
                <div className="container mx-auto py-8 px-4">
                    <div className="text-center max-w-2xl mx-auto mb-8">
                        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                            Restoranlar
                        </h1>
                        <div className="flex justify-center gap-8 text-gray-600">
                            <div className="flex items-center gap-2">
                                <FaStore className="text-orange-600"/>
                                <span>{firms.length} Restoran</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <FaClock className="text-green-600"/>
                                <span>{activeCount} Açık</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <FaBan className="text-red-600"/>
                                <span>{firms.length - activeCount} Kapalı</span>
                            </div>
                        </div>
                    </div>

                    {/* Filtreler */}
                    <div className="flex flex-col md:flex-row gap-4">
                        {/* Arama */}
                        <div className="relative flex-1">
                            <FaSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400"/>
                            <input
                                type="text"
                                placeholder="Restoran ara..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200
                                         focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            />
                        </div>

                        {/* Kategori Seçimi */}
                        <div className="relative min-w-[200px]">
                            <FaFilter className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400"/>
                            <select
                                value={selectedCategory}
                                onChange={(e) => setSelectedCategory(e.target.value)}
                                className={`w-full pl-12 pr-10 py-3 rounded-xl border border-gray-200 
                                          appearance-none bg-white focus:ring-2 focus:ring-orange-500 
                                          focus:border-transparent ${
                                    loadingCategories ? 'text-gray-400' : 'text-gray-700'
                                }`}
                                disabled={loadingCategories}
                            >
                                <option value="all">Tüm Kategoriler</option>
                                {loadingCategories ? (
                                    <option disabled>Yükleniyor...</option>
                                ) : (
                                    categories.map(category => (
                                        <option key={category.id} value={category.id}>
                                            {category.name}
                                        </option>
                                    ))
                                )}
                            </select>
                            <div className="absolute right-4 top-1/2 -translate-y-1/2 pointer-events-none">
                                {loadingCategories ? (
                                    <FaSpinner className="animate-spin text-gray-400"/>
                                ) : (
                                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor"
                                         viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                              d="M19 9l-7 7-7-7"/>
                                    </svg>
                                )}
                            </div>
                        </div>

                        {/* Açık/Kapalı Filtresi */}
                        <button
                            onClick={() => setShowOnlyOpen(!showOnlyOpen)}
                            className={`px-6 py-3 rounded-xl font-medium transition-all whitespace-nowrap
                                     ${showOnlyOpen
                                ? 'bg-orange-600 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                        >
                            {showOnlyOpen ? '✓ Sadece Açık' : 'Sadece Açık Restoranlar'}
                        </button>
                    </div>
                </div>
            </div>

            {/* Ana İçerik */}
            <div className="container mx-auto py-8 px-4">
                {/* Filtreleme Sonuç Bilgisi */}
                <div className="mb-6">
                    <div className="text-sm text-gray-600">
                        <span className="font-medium text-gray-800">{filteredFirms.length}</span> restoran bulundu
                        {searchTerm && <span> &quot;{searchTerm}&quot; için</span>}
                        {selectedCategory !== 'all' && (
                            <span> {categories.find(c => c.id === selectedCategory)?.name} kategorisinde</span>
                        )}
                        {showOnlyOpen && ' (Sadece açık restoranlar)'}
                    </div>
                </div>

                {/* Restoranlar Grid */}
                {filteredFirms.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {filteredFirms.map((firm) => (
                            <FirmCard key={firm.id} firm={firm}/>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-16 bg-white rounded-xl shadow-sm">
                        <FaStore className="text-6xl text-gray-300 mx-auto mb-4"/>
                        <h3 className="text-xl font-semibold text-gray-700 mb-2">
                            Restoran Bulunamadı
                        </h3>
                        <p className="text-gray-500 max-w-md mx-auto">
                            Arama kriterlerinize uygun restoran bulamadık.
                            Lütfen farklı bir arama yapmayı veya filtreleri değiştirmeyi deneyin.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
}