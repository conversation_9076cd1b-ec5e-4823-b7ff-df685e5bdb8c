'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getAuth, RecaptchaVerifier, PhoneAuthProvider, signInWithCredential } from 'firebase/auth';
import { app, db } from '@/config/firebaseConfig';
import { doc, setDoc } from 'firebase/firestore';

function VerifyOtpContent() {
    const [otp, setOtp] = useState('');
    const [error, setError] = useState('');
    const router = useRouter();
    const searchParams = useSearchParams();
    const auth = getAuth(app);

    const verificationId = searchParams.get('verificationId');
    const firstName = searchParams.get('firstName');
    const lastName = searchParams.get('lastName');
    const phoneNumber = searchParams.get('phoneNumber');
    const action = searchParams.get('action');

    useEffect(() => {
        if (typeof window !== 'undefined' && !window.recaptchaVerifier) {
            window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', { size: 'invisible' });
            window.recaptchaVerifier.render();
        }
    }, [auth]);

    const handleVerifyOtp = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!verificationId) {
            setError('Doğrulama kimliği bulunamadı');
            return;
        }

        try {
            const credential = PhoneAuthProvider.credential(verificationId, otp);
            const userCredential = await signInWithCredential(auth, credential);
            const uid = userCredential.user.uid;

            if (action !== 'signin') {
                // Add user to Firestore using UID
                await setDoc(doc(db, 'users', uid), {
                    firstName,
                    lastName,
                    phoneNumber,
                    role: 'user'
                });
            }

            router.push('/'); // Redirect to home page after successful verification
        } catch (err) {
            console.error('Error during OTP verification:', err);
            setError('SMS doğrulaması başarısız. Lütfen tekrar deneyin.');
        }
    };

    return (
        <div className="min-h-[50vh] flex items-center justify-center bg-gray-100">
            <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
                <h2 className="text-2xl font-bold mb-6 text-center">SMS Doğrulama</h2>
                {error && <p className="text-red-500 mb-4">{error}</p>}
                <form onSubmit={handleVerifyOtp}>
                    <div className="mb-4">
                        <label htmlFor="otp" className="block text-gray-700 mb-2">OTP</label>
                        <input
                            type="text"
                            id="otp"
                            value={otp}
                            onChange={(e) => setOtp(e.target.value)}
                            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition-colors duration-300"
                    >
                        Doğrula
                    </button>
                </form>
                <div id="recaptcha-container" className="mt-4"></div>
            </div>
        </div>
    );
}

export default function VerifyOtpPage() {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <VerifyOtpContent />
        </Suspense>
    );
}