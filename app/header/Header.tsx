'use client';
import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { FaShoppingCart, FaBell, FaSignInAlt, FaUserPlus, FaUser } from 'react-icons/fa';
import { getAuth, onAuthStateChanged, User } from 'firebase/auth';
import { collection, onSnapshot } from 'firebase/firestore';
import { app, db } from '@/config/firebaseConfig';

export default function Header() {
    const router = useRouter();
    const auth = getAuth(app);
    const [cartItemCount, setCartItemCount] = useState<number>(0);
    const [user, setUser] = useState<User | null>(null);

    useEffect(() => {
        let unsubscribeCart: (() => void) | undefined;

        const unsubscribeAuth = onAuthStateChanged(auth, (currentUser) => {
            setUser(currentUser);
            
            if (unsubscribeCart) {
                unsubscribeCart();
            }

            if (currentUser) {
                const cartRef = collection(db, 'users', currentUser.uid, 'activeCartWeb', 'cart', 'items');
                unsubscribeCart = onSnapshot(cartRef, (snapshot) => {
                    setCartItemCount(snapshot.docs.length);
                });
            } else {
                setCartItemCount(0);
            }
        });

        return () => {
            unsubscribeAuth();
            if (unsubscribeCart) {
                unsubscribeCart();
            }
        };
    }, [auth]);

    const NavLink = ({ href, children }: { href: string; children: React.ReactNode }) => (
        <Link
            href={href}
            className="hover:text-orange-200 transition-colors duration-300 font-medium"
        >
            {children}
        </Link>
    );

    const IconButton = ({
                            icon: Icon,
                            label,
                            onClick,
                            badge
                        }: {
        icon: typeof FaUser;
        label?: string;
        onClick?: () => void;
        badge?: number;
    }) => (
        <button
            onClick={onClick}
            className="flex items-center space-x-2 hover:text-orange-200 transition-colors duration-300"
        >
            <div className="relative">
                <Icon className="text-xl" />
                {badge !== undefined && badge > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full text-xs w-5 h-5 flex items-center justify-center">
                        {badge}
                    </span>
                )}
            </div>
            {label && <span className="font-medium">{label}</span>}
        </button>
    );

    return (
        <header className="bg-gradient-to-r from-orange-600 to-orange-500 text-white shadow-lg">
            <div className="container mx-auto px-4 py-3">
                <div className="flex items-center justify-between">
                    {/* Logo Section */}
                    <Link href="/" className="flex items-center space-x-3 hover:opacity-90 transition-opacity duration-300">
                        <Image
                            src="/app_logo.png"
                            alt="Yemek Kapımda Logo"
                            width={45}
                            height={45}
                            className="rounded-lg"
                        />
                        <h1 className="text-2xl font-bold tracking-wide">Yemek Kapımda</h1>
                    </Link>

                    {/* Navigation Links */}
                    <nav className="hidden md:flex space-x-8">
                        <NavLink href="/">Anasayfa</NavLink>
                        <NavLink href="/all-firms">Restoranlar</NavLink>
                        <NavLink href="/categories">Kategoriler</NavLink>
                    </nav>

                    {/* User Actions */}
                    <div className="flex items-center space-x-6">
                        {user ? (
                            <>
                                <IconButton icon={FaBell} />
                                <IconButton
                                    icon={FaShoppingCart}
                                    onClick={() => router.push('/cart')}
                                    badge={cartItemCount}
                                />
                                <NavLink href="/account">
                                    <div className="flex items-center space-x-2">
                                        <FaUser />
                                        <span>Hesabım</span>
                                    </div>
                                </NavLink>
                            </>
                        ) : (
                            <>
                                <IconButton icon={FaSignInAlt} label="Giriş Yap" onClick={() => router.push('/signin')} />
                                <IconButton icon={FaUserPlus} label="Kayıt Ol" onClick={() => router.push('/signup')} />
                            </>
                        )}
                    </div>
                </div>
            </div>
        </header>
    );
}