import React from "react";

const ContactPage = () => {
    return (
        <div className="container mx-auto p-6 bg-white shadow-lg rounded-md my-10">
            <h1 className="text-3xl font-bold mb-4 text-gray-800 text-center">
                <PERSON><PERSON><PERSON><PERSON><PERSON>
            </h1>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h2 className="text-2xl font-semibold mb-4 text-gray-800"><PERSON><PERSON>işim Bilgileri</h2>
                    <p className="text-gray-700 mb-4">
                        Firma Ünvanı: Abdullah <PERSON> YEMEK KAPIMDA <br />
                        Adres: Hacı Seyit Ali mahallesi 153086 sok no 20 c Seydişehir Konya <br />
                        Telefon: +90 ************ <br />
                        E-posta: <EMAIL>
                    </p>
                </div>
                <div>
                    <h2 className="text-2xl font-semibold mb-4 text-gray-800">Bize Ulaşın</h2>
                    <form className="space-y-4">
                        <div>
                            <label className="block text-gray-700">Ad-Soyad</label>
                            <input type="text" className="w-full p-2 border border-gray-300 rounded-md" />
                        </div>
                        <div>
                            <label className="block text-gray-700">E-posta</label>
                            <input type="email" className="w-full p-2 border border-gray-300 rounded-md" />
                        </div>
                        <div>
                            <label className="block text-gray-700">Mesaj</label>
                            <textarea className="w-full p-2 border border-gray-300 rounded-md" rows={4}></textarea>
                        </div>
                        <button type="submit" className="w-full bg-orange-500 text-white p-2 rounded-md hover:bg-orange-600">
                            Gönder
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ContactPage;