'use client';
import './globals.css';
import Header from '@/app/header/Header';
import Footer from '@/app/footer/Footer';



export default function Layout({ children }: { children: React.ReactNode }) {    return (
        <html lang="en">
        <head>
            <title><PERSON><PERSON><PERSON></title>
            <meta charSet="UTF-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            <meta name="description" content="Konya Seydişehir on-demand delivery önde gelen firması Yemek Kapımda." />
            <link rel="icon" href="/favicon.ico" />

        </head>
        <body>
        <Header />
        <main>{children}</main>
        <Footer />
        </body>
        </html>
    );
}
