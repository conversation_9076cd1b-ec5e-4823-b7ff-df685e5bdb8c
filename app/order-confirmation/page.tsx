'use client';
import React, { useEffect, useState } from 'react';
import { getAuth } from 'firebase/auth';
import { db } from '@/config/firebaseConfig';
import { collection, getDocs, query, where, Timestamp,   } from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

interface OrderItem {
    id: string;
    productName: string;
    price: number;
    quantity: number;
    imageURL: string;
    firmName: string;
    categoryName: string;
}



interface Order {
    userId: string;
    items: OrderItem[];
    totalPrice: number;
    finalPrice: number;
    addresses: {
        id: string;
        aciklama: string;
        bina_ve_daire: string;
        mahalle: string;
        sokak: string;
    }[];
    deliverTo: string;
    deliveryInstructions: string[];
    paymentMethod: string;
    instructions: string;
    orderDate: Timestamp;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    status: string;
}

export default function OrderConfirmationPage() {
    const [order, setOrder] = useState<Order | null>(null);
    const auth = getAuth();
    const router = useRouter();

    useEffect(() => {
        const fetchOrder = async () => {
            try {
                const user = auth.currentUser;
                if (!user) {
                    router.push('/signin');
                    return;
                }

                const ordersRef = collection(db, 'orders');
                const q = query(ordersRef, where('userId', '==', user.uid));
                const querySnapshot = await getDocs(q);

                if (!querySnapshot.empty) {
                    const orderData = querySnapshot.docs[0].data() as Order;
                    setOrder(orderData);
                } else {
                    console.error('Sipariş bulunamadı');
                }
            } catch (error) {
                console.error('Hata:', error);
            }
        };

        fetchOrder();
    }, [auth, router]);

    if (!order) {
        return <div>Yükleniyor...</div>;
    }

    return (
        <div className="container mx-auto py-10 px-4">
            <h1 className="text-3xl font-bold mb-6">Sipariş Onayı</h1>
            <div className="mb-6">
                <h2 className="text-xl font-bold">Sipariş Detayları</h2>
                <p><strong>Sipariş Tarihi:</strong> {order.orderDate.toDate().toLocaleString()}</p>
                {order.addresses?.map(address => 
                    address.id === order.deliverTo && (
                        <p key={address.id}>
                            <strong>Teslimat Adresi:</strong> {address.sokak}, {address.mahalle}, {' '}
                            {address.bina_ve_daire}
                            {address.aciklama && `, ${address.aciklama}`}
                        </p>
                    )
                )}
                <p><strong>Ödeme Yöntemi:</strong> {order.paymentMethod}</p>
                {order.instructions && (
                    <p><strong>Açıklamalar:</strong> {order.instructions}</p>
                )}
                {order.deliveryInstructions && order.deliveryInstructions.length > 0 && (
                    <p><strong>Teslimat Talimatları:</strong> {order.deliveryInstructions.join(', ')}</p>
                )}
                <p><strong>Toplam Fiyat:</strong> {order.totalPrice} TL</p>
                {order.totalPrice !== order.finalPrice && (
                    <p><strong>İndirimli Fiyat:</strong> {order.finalPrice} TL</p>
                )}
            </div>
            <div className="mb-6">
                <h2 className="text-xl font-bold">Ürünler</h2>
                <ul>
                    {order.items.map(item => (
                        <li key={item.id} className="flex items-center mb-4 p-4 border rounded-lg shadow-md">
                            <Image src={item.imageURL} alt={item.productName} width={80} height={80} className="w-20 h-20 object-cover mr-4 rounded-lg" />
                            <div className="flex-1">
                                <h2 className="text-xl font-bold">{item.productName}</h2>
                                <p className="text-gray-700">{item.price} TL x {item.quantity}</p>
                                <p className="text-gray-500">Firma: {item.firmName}</p>
                                <p className="text-gray-500">Kategori: {item.categoryName}</p>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
            <div className="text-center mt-6">
                <p className="text-lg font-bold">Siparişiniz alınmıştır, sipariş durumunuzu Hesabım &gt; Siparişlerim sayfasından takip edebilirsiniz.</p>
            </div>
        </div>
    );
}