'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getAuth, RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import { app, db } from '@/config/firebaseConfig';
import { collection, query, where, getDocs } from 'firebase/firestore';

export default function SignInPage() {
    const [phoneNumber, setPhoneNumber] = useState('');
    const [error, setError] = useState('');
    const router = useRouter();
    const auth = getAuth(app);
    const [recaptchaVerifier, setRecaptchaVerifier] = useState<RecaptchaVerifier | null>(null);

    useEffect(() => {
        if (typeof window !== 'undefined' && !recaptchaVerifier) {
            const verifier = new RecaptchaVerifier(auth, 'recaptcha-container', { size: 'invisible' });
            verifier.render();
            setRecaptchaVerifier(verifier);
        }
    }, [recaptchaVerifier, auth]);

    const handleSignIn = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!recaptchaVerifier) {
            setError('RecaptchaVerifier başlatılamadı');
            return;
        }

        try {
            const fullPhoneNumber = `+90${phoneNumber.trim()}`;
            const usersRef = collection(db, 'users');
            const q = query(usersRef, where("phoneNumber", "==", fullPhoneNumber));
            const querySnapshot = await getDocs(q);

            if (querySnapshot.empty) {
                setError('Kullanıcı bulunamadı. Lütfen kayıt olun.');
                return;
            }

            const userUid = querySnapshot.docs[0].id;

            const confirmationResult = await signInWithPhoneNumber(auth, fullPhoneNumber, recaptchaVerifier);
            const verificationId = confirmationResult.verificationId;
            router.push(`/verify-otp?verificationId=${verificationId}&phoneNumber=${fullPhoneNumber}&userUid=${userUid}&action=signin`);
        } catch (err: any) {
            console.error('Error during sign-in:', err);

            // Firebase hata kodlarına göre özel mesajlar
            if (err.code === 'auth/too-many-requests') {
                setError('Çok fazla istek gönderildi. Lütfen 1 saat sonra tekrar deneyin.');
            } else if (err.code === 'auth/quota-exceeded') {
                setError('SMS kotası doldu. Lütfen daha sonra tekrar deneyin.');
            } else if (err.message?.includes('503') || err.message?.includes('Service Unavailable')) {
                setError('SMS servisi geçici olarak kullanılamıyor. Lütfen 10-15 dakika sonra tekrar deneyin.');
            } else if (err.code === 'auth/invalid-phone-number') {
                setError('Geçersiz telefon numarası. Lütfen kontrol edin.');
            } else {
                setError(`SMS gönderilemedi: ${err.message || 'Bilinmeyen hata'}`);
            }
        }
    };

    return (
        <div className="min-h-[50vh] flex items-center justify-center bg-gray-100">
            <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
                <h2 className="text-2xl font-bold mb-6 text-center">Giriş Yap</h2>
                {error && <p className="text-red-500 mb-4">{error}</p>}
                <form onSubmit={handleSignIn}>
                    <div className="mb-4">
                        <label htmlFor="phoneNumber" className="block text-gray-700 mb-2">Telefon Numarası</label>
                        <div className="flex">
                            <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">+90</span>
                            <input
                                type="tel"
                                id="phoneNumber"
                                value={phoneNumber}
                                onChange={(e) => setPhoneNumber(e.target.value)}
                                className="w-full px-4 py-2 border rounded-r-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                                required
                            />
                        </div>
                    </div>
                    <button
                        type="submit"
                        className="w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition-colors duration-300"
                    >
                        SMS Gönder
                    </button>
                </form>
                <div id="recaptcha-container" className="mt-4"></div>
                <div className="mt-4 text-center">
                    <p className="text-gray-700">Hesabınız yok mu? <a href="/signup" className="text-orange-600 hover:text-orange-800">Kayıt Olun</a></p>
                </div>
            </div>
        </div>
    );
}