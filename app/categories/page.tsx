'use client';
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useFirestoreData } from '@/hooks/useFirestoreData';
import { FaSpinner } from 'react-icons/fa';

export default function CategoriesPage() {
    const { allCategories, loading } = useFirestoreData();

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-[60vh]">
                <div className="text-center">
                    <FaSpinner className="animate-spin text-4xl text-orange-600 mx-auto mb-4" />
                    <p className="text-gray-600">Kategoriler yükleniyor...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-12">
            <div className="container mx-auto px-4">
                {/* Başlık */}
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-gray-800 mb-4">
                        <PERSON>gorilerimiz
                    </h1>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                        İhtiyacın<PERSON>z olan her şey kategorilere ayrılmış şekilde karşınızda.
                        Dilediğiniz kategoriyi seçerek alışverişe başlayabilirsiniz.
                    </p>
                    <div className="w-24 h-1.5 bg-orange-600 mx-auto mt-6 rounded-full" />
                </div>

                {/* Kategori Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {allCategories.map((category) => (
                        <Link
                            key={category.id}
                            href={`/categories/${category.id}`}
                            className="group relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-xl
                                     transition-all duration-300 hover:-translate-y-1"
                        >
                            {/* Görsel Alanı */}
                            <div className="relative h-48 overflow-hidden">
                                <Image
                                    src={category.imageUrl}
                                    alt={category.name}
                                    width={400}
                                    height={200}
                                    className="w-full h-full object-cover transition-transform duration-500
                                             group-hover:scale-110"
                                />
                                {/* Gradient Overlay */}
                                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0
                                              group-hover:opacity-100 transition-opacity duration-300" />
                            </div>

                            {/* Kategori İçerik */}
                            <div className="p-6">
                                <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-orange-600
                                             transition-colors">
                                    {category.name}
                                </h3>

                            </div>

                            {/* Hover Efekti */}
                            <div className="absolute inset-0 border-4 border-orange-600 rounded-2xl opacity-0
                                          group-hover:opacity-100 transition-opacity duration-300" />
                        </Link>
                    ))}
                </div>

                {/* Boş Durum */}
                {allCategories.length === 0 && (
                    <div className="text-center py-12">
                        <p className="text-gray-600 text-lg">
                            Henüz hiç kategori bulunmuyor.
                        </p>
                    </div>
                )}

                {/* Alt Bilgi */}
                <div className="text-center mt-12">
                    <p className="text-sm text-gray-500">
                        Tüm kategorilerimizi keşfedin ve size en uygun ürünleri bulun.
                    </p>
                </div>
            </div>
        </div>
    );
}