'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useFirestoreData } from '@/hooks/useFirestoreData';
import FirmCard from '@/app/components/All-firms/FirmCard';

export default function CategoryFirmsPage() {
    const params = useParams();
    const categoryId = Array.isArray(params.categoryName) ? params.categoryName[0] : params.categoryName; // Ensure this is a string
    const { firms } = useFirestoreData(categoryId);

    return (
        <div className="container mx-auto py-10">
            <h2 className="text-2xl font-medium mb-6 text-center relative">
                Restoranlar
                <span className="block w-16 h-1 bg-orange-600 mx-auto mt-2"></span>
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {firms.length > 0 ? (
                    firms.map((firm, index) => (
                        <FirmCard key={index} firm={firm} />
                    ))
                ) : (
                    <p className="text-center w-full">Firmalar Yükleniyor</p>
                )}
            </div>
        </div>
    );
}