// app/page.tsx
'use client';

import React from 'react';
import { useFirestoreData } from '../hooks/useFirestoreData';
import Banner from './components/Home/Banner';
import CategoryList from './components/Home/CategoryList';
import FirmList from './components/Home/FirmList';
import SearchBar from './components/Home/SearchBar';

export default function HomePage() {
    const { firms, categories } = useFirestoreData();

    // Sort firms so that visible firms are at the top
    const sortedFirms = [...firms].sort((a, b) => Number(b.visible) - Number(a.visible));

    return (
        <>
            <head>
                <title>Ana sayfa | Yemek <PERSON>ı<PERSON>da</title>
                <meta charSet="UTF-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta name="description" content="Seydişehir Yemek Kapımda Ana Sayfasıdır" />
                <meta name="author" content="<PERSON><PERSON><PERSON>" />
                <meta property="og:type" content="website" />
                <meta property="og:title" content="Yemek Kapımda" />
                <meta property="og:description" content="Konya Seydişehir on-demand delivery önde gelen firması Yemek Kapımda." />
                <meta property="og:url" content="https://www.yemekkapimda.app/" />
                <meta property="og:image" content="https://yemekkapimda.app/_next/image?url=%2Fapp_logo.png&w=96&q=75" />
                <link rel="icon" href="/favicon.ico" />
            </head>
            <Banner />
            <SearchBar />
            <CategoryList categories={categories} />
            <FirmList firms={sortedFirms} />
        </>
    );
}