'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import { auth } from '@/config/firebaseConfig';

export default function SignUpPage() {
    const [firstName, setFirstName] = useState<string>('');
    const [lastName, setLastName] = useState<string>('');
    const [phoneNumber, setPhoneNumber] = useState<string>('');
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const router = useRouter();
    const [recaptchaVerifier, setRecaptchaVerifier] = useState<RecaptchaVerifier | null>(null);

    useEffect(() => {
        if (typeof window !== 'undefined' && !recaptchaVerifier) {
            const verifier = new RecaptchaVerifier(
                auth,
                'recaptcha-container',
                { size: 'invisible' },
            );
            verifier.render();
            setRecaptchaVerifier(verifier);
        }
    }, [recaptchaVerifier]);

    const onSignUpSubmit = async (event: React.FormEvent) => {
        event.preventDefault();

        if (!recaptchaVerifier) {
            setErrorMessage('RecaptchaVerifier is not initialized');
            return;
        }

        if (!/^\d{10}$/.test(phoneNumber)) {
            setErrorMessage('Telefon numarası formatı geçersiz');
            return;
        }

        try {
            const fullPhoneNumber = `+90${phoneNumber.trim().replace(/\s+/g, '')}`;
            const encodedPhoneNumber = encodeURIComponent(fullPhoneNumber);

            const confirmationResult = await signInWithPhoneNumber(
                auth,
                fullPhoneNumber,
                recaptchaVerifier
            );
            const verificationId = confirmationResult.verificationId;

            router.push(
                `/verify-otp?verificationId=${verificationId}&firstName=${firstName}&lastName=${lastName}&phoneNumber=${encodedPhoneNumber}`
            );
        } catch (error: any) {
            console.error('Error during signup:', error);

            if (error.code === 'auth/too-many-requests') {
                setErrorMessage('Çok fazla istek gönderildi. Lütfen 1 saat sonra tekrar deneyin.');
            } else if (error.code === 'auth/quota-exceeded') {
                setErrorMessage('SMS kotası doldu. Lütfen daha sonra tekrar deneyin.');
            } else if (error.message?.includes('503') || error.message?.includes('Service Unavailable')) {
                setErrorMessage('SMS servisi geçici olarak kullanılamıyor. Lütfen 10-15 dakika sonra tekrar deneyin.');
            } else if (error.code === 'auth/invalid-phone-number') {
                setErrorMessage('Geçersiz telefon numarası. Lütfen kontrol edin.');
            } else {
                setErrorMessage(`Kayıt Hatası: ${error.message || 'Bilinmeyen hata'}`);
            }
        }
    };

    return (
        <div className="flex items-center justify-center min-h-[50vh] bg-gray-100">
            <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
                <h1 className="text-2xl font-bold mb-6 text-center">Kayıt Ol</h1>
                <form onSubmit={onSignUpSubmit} className="space-y-4">
                    <div>
                        <label className="block text-gray-700">Ad</label>
                        <input
                            type="text"
                            placeholder="Ad"
                            value={firstName}
                            onChange={(e) => setFirstName(e.target.value)}
                            required
                            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                        />
                    </div>
                    <div>
                        <label className="block text-gray-700">Soyad</label>
                        <input
                            type="text"
                            placeholder="Soyad"
                            value={lastName}
                            onChange={(e) => setLastName(e.target.value)}
                            required
                            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                        />
                    </div>
                    <div>
                        <label className="block text-gray-700">Telefon Numarası</label>
                        <div className="flex">
                            <span
                                className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                +90
                            </span>
                            <input
                                type="tel"
                                placeholder="5XX XXX XX XX"
                                value={phoneNumber}
                                onChange={(e) => setPhoneNumber(e.target.value)}
                                required
                                className="w-full px-4 py-2 border rounded-r-lg focus:outline-none focus:ring-2 focus:ring-orange-600"
                            />
                        </div>
                    </div>
                    {errorMessage && (
                        <div className="text-red-500 text-sm mt-2">
                            {errorMessage}
                        </div>
                    )}
                    <button
                        type="submit"
                        className="w-full py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-300"
                    >
                        Kayıt Ol
                    </button>
                </form>
                <div id="recaptcha-container" className="mt-4"></div>
                <div className="mt-4 text-center">
                    <p className="text-gray-700">Hesabınız var mı? <a href="/signin" className="text-orange-600 hover:text-orange-800">Giriş Yapın</a></p>
                </div>
            </div>
        </div>
    );
}