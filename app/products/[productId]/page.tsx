'use client';

import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { db } from '@/config/firebaseConfig';
import { doc, getDoc, collection, addDoc, serverTimestamp, query, where, getDocs, limit } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { FaPlus, FaMinus, FaStore, FaPhoneAlt, FaShare, FaTag, FaClock, FaExclamationCircle } from 'react-icons/fa';

interface Product {
    id: string;
    name: string;
    description: string;
    images: string[];
    category: string;
    price: number | string;
    stockStatus: string;
    firmId: string;
    features?: { name: string; price: string | number }[];
}

interface Firm {
    id: string;
    name: string;
    visible: boolean;
    description?: string;
    preparationTime?: string;
}

interface Category {
    id: string;
    name: string;
}

const formatPrice = (price: number | string): number => {
    const numPrice = Number(price);
    if (isNaN(numPrice)) return 0;
    if (numPrice > 1000 && numPrice % 10 === 0) return numPrice / 10;
    return numPrice;
};

export default function ProductDetailPage() {
    const { productId } = useParams();
    const [product, setProduct] = useState<Product | null>(null);
    const [firm, setFirm] = useState<Firm | null>(null);
    const [category, setCategory] = useState<Category | null>(null);
    const [selectedFeatures, setSelectedFeatures] = useState<{ name: string; price: string | number }[]>([]);
    const [isFeatureSelected, setIsFeatureSelected] = useState(false);
    const [noFeatureSelected, setNoFeatureSelected] = useState(false);
    const [message, setMessage] = useState<string>('');
    const [similarProducts, setSimilarProducts] = useState<Product[]>([]);
    const [selectedImage, setSelectedImage] = useState(0);
    const router = useRouter();
    const auth = getAuth();

    useEffect(() => {
        const fetchDetails = async () => {
            if (!productId || Array.isArray(productId)) return;

            try {
                // Ürün detaylarını getir
                const productDoc = await getDoc(doc(db, 'products', productId));
                if (productDoc.exists()) {
                    const data = productDoc.data();
                    const productData = {
                        id: productId,
                        ...data,
                        price: formatPrice(data.price),
                        features: data.features?.map((feature: { name: string; price: string | number }) => ({
                            ...feature,
                            price: formatPrice(feature.price)
                        }))
                    } as Product;
                    setProduct(productData);

                    // Firma detaylarını getir
                    const firmDoc = await getDoc(doc(db, 'firms', productData.firmId));
                    if (firmDoc.exists()) {
                        setFirm({ id: firmDoc.id, ...firmDoc.data() } as Firm);
                    }

                    // Kategori detaylarını getir
                    const categoryDoc = await getDoc(doc(db, 'categories', productData.category));
                    if (categoryDoc.exists()) {
                        setCategory({ id: categoryDoc.id, ...categoryDoc.data() } as Category);
                    }

                    // Benzer ürünleri getir
                    const similarProductsQuery = query(
                        collection(db, 'products'),
                        where('category', '==', productData.category),
                        where('firmId', '==', productData.firmId),
                        limit(4)
                    );
                    const similarProductsSnapshot = await getDocs(similarProductsQuery);
                    const similarProductsData = similarProductsSnapshot.docs
                        .map(doc => ({ id: doc.id, ...doc.data() } as Product))
                        .filter(p => p.id !== productId && p.stockStatus === "Stokta Var");
                    setSimilarProducts(similarProductsData);
                }
            } catch (error) {
                console.error('Error fetching product details:', error);
                setMessage('Ürün detayları yüklenirken bir hata oluştu.');
            }
        };

        fetchDetails();
    }, [productId]);

    const calculateTotalPrice = () => {
        if (!product) return 0;
        const basePrice = formatPrice(product.price);
        const featuresTotal = selectedFeatures.reduce((sum, feature) => {
            return sum + formatPrice(feature.price);
        }, 0);
        return basePrice + featuresTotal;
    };

    const handleAddToCart = async () => {
        if (!firm?.visible) {
            setMessage('İşletme şu anda kapalı!');
            setTimeout(() => setMessage(''), 3000);
            return;
        }

        if (product?.features?.length && !isFeatureSelected && selectedFeatures.length === 0 && !noFeatureSelected) {
            setMessage('Lütfen bir özellik seçin veya "İstemiyorum" seçeneğini işaretleyin.');
            return;
        }

        const user = auth.currentUser;
        if (!user) {
            router.push('/signin');
            return;
        }

        if (!product) return;

        try {
            const cartRef = collection(db, 'users', user.uid, 'activeCartWeb', 'cart', 'items');
            const cartItem = {
                addedAt: serverTimestamp(),
                categoryId: product.category,
                categoryName: category?.name,
                firmId: product.firmId,
                firmName: firm?.name,
                imageURL: product.images[0],
                price: calculateTotalPrice(),
                productId: product.id,
                productName: product.name,
                quantity: 1,
                selectedFeatures: selectedFeatures.map(feature => ({
                    name: feature.name,
                    price: formatPrice(feature.price)
                })),
                itemStatus: 'Sipariş Alındı'
            };
            await addDoc(cartRef, cartItem);

            setMessage('Ürün sepete eklendi!');
            setTimeout(() => setMessage(''), 3000);
        } catch (error) {
            console.error('Error adding to cart:', error);
            setMessage('Ürün sepete eklenirken bir hata oluştu.');
            setTimeout(() => setMessage(''), 3000);
        }
    };

    const toggleFeature = (feature: { name: string; price: string | number }) => {
        if (!firm?.visible) return;

        const updatedFeatures = selectedFeatures.some(f => f.name === feature.name)
            ? selectedFeatures.filter(f => f.name !== feature.name)
            : [...selectedFeatures, feature];

        setSelectedFeatures(updatedFeatures);
        setIsFeatureSelected(true);
        setNoFeatureSelected(false);
    };

    const handleNoFeature = () => {
        if (!firm?.visible) return;
        setSelectedFeatures([]);
        setIsFeatureSelected(true);
        setNoFeatureSelected(true);
    };

    const handleCallSupport = () => {
        window.location.href = 'tel:+905435820442';
    };

    const handleShare = async () => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: product?.name,
                    text: `${product?.name} - ${firm?.name}`,
                    url: window.location.href
                });
            } catch (error) {
                console.error('Error sharing:', error);
            }
        } else {
            navigator.clipboard.writeText(window.location.href);
            setMessage('Link kopyalandı!');
            setTimeout(() => setMessage(''), 3000);
        }
    };

    if (!product || !firm) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <p className="text-xl text-gray-600">Yükleniyor...</p>
            </div>
        );
    }

    return (
        <div className="container mx-auto py-10 px-4">
            {/* İşletme Kapalı Uyarısı */}
            {!firm.visible && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                    <div className="flex items-center gap-2">
                        <FaExclamationCircle className="text-xl" />
                        <p className="font-medium">
                            Bu işletme şu anda kapalıdır. Siparişler geçici olarak alınmamaktadır.
                        </p>
                    </div>
                </div>
            )}

            {/* Firma Bilgi Kartı */}
            <div className="mb-6">
                <Link href={`/firms/${firm.id}`}
                      className="flex items-center gap-3 p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                    <FaStore className="text-orange-600 text-xl" />
                    <div>
                        <h3 className="text-lg font-semibold text-orange-600">{firm.name}</h3>
                        {firm.description && <p className="text-sm text-gray-600">{firm.description}</p>}
                        {firm.preparationTime && (
                            <div className="flex items-center gap-2 mt-1 text-sm text-gray-500">
                                <FaClock />
                                <span>Hazırlanma Süresi: {firm.preparationTime}</span>
                            </div>
                        )}
                    </div>
                </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                {/* Sol Kolon: Ürün Görselleri */}
                <div className="space-y-4">
                    <div className="relative w-full pt-[100%] bg-white rounded-lg shadow-lg overflow-hidden">
                        <Image
                            src={product.images[selectedImage]}
                            alt={product.name}
                            layout="fill"
                            objectFit="contain"
                            className={`absolute top-0 left-0 w-full h-full ${!firm.visible ? 'filter grayscale' : ''}`}
                        />
                    </div>

                    {/* Küçük Görsel Galerisi */}
                    {product.images.length > 1 && (
                        <div className="grid grid-cols-4 gap-2">
                            {product.images.map((image, index) => (
                                <button
                                    key={index}
                                    onClick={() => setSelectedImage(index)}
                                    className={`relative pt-[100%] rounded-lg overflow-hidden border-2 
                                    ${selectedImage === index ? 'border-orange-500' : 'border-gray-200'}`}
                                >
                                    <Image
                                        src={image}
                                        alt={`${product.name} ${index + 1}`}
                                        layout="fill"
                                        objectFit="cover"
                                        className={!firm.visible ? 'filter grayscale' : ''}
                                    />
                                </button>
                            ))}
                        </div>
                    )}
                </div>

                {/* Sağ Kolon: Ürün Bilgileri */}
                <div className="space-y-6">
                    {/* Başlık ve Kategori */}
                    <div>
                        <div className="flex items-center gap-2 mb-2">
                            <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
                            {!firm.visible && (
                                <span className="bg-red-600 text-white px-3 py-1 rounded-full text-sm">
                                    İşletme Kapalı
                                </span>
                            )}
                        </div>
                        {category && (
                            <div className="flex items-center gap-2 text-gray-600">
                                <FaTag className="text-orange-600" />
                                <span>{category.name}</span>
                            </div>
                        )}
                    </div>

                    {/* Fiyat */}
                    <div className="bg-orange-50 p-4 rounded-lg">
                        <p className="text-3xl font-bold text-orange-600">
                            {calculateTotalPrice().toFixed(2)} ₺
                        </p>
                    </div>

                    {/* Açıklama */}
                    {product.description && (
                        <div className="prose max-w-none">
                            <p className="text-gray-600">{product.description}</p>
                        </div>
                    )}

                    {/* Özellikler */}
                    {product.features && product.features.length > 0 && (
                        <div className={`bg-gray-50 p-6 rounded-lg ${!firm.visible ? 'opacity-50' : ''}`}>
                            <h3 className="text-xl font-semibold mb-4">Özellikler</h3>
                            <p className="text-red-500 font-medium mb-4">Birini seç</p>
                            <ul className="space-y-3">
                                {product.features.map((feature, index) => (
                                    <li key={index}
                                        className="flex items-center justify-between p-2 bg-white rounded-lg shadow-sm">
                                        <span className="text-gray-700">
                                            {feature.name}: {formatPrice(feature.price).toFixed(2)} ₺
                                        </span>
                                        <button
                                            onClick={() => toggleFeature(feature)}
                                            className={`p-2 rounded-md transition-colors ${
                                                selectedFeatures.some(f => f.name === feature.name)
                                                    ? 'bg-orange-600 text-white'
                                                    : firm.visible
                                                        ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            }`}
                                            disabled={!firm.visible}
                                        >
                                            {selectedFeatures.some(f => f.name === feature.name) ? <FaMinus /> : <FaPlus />}
                                        </button>
                                    </li>
                                ))}
                                <li className="flex items-center justify-between p-2 bg-white rounded-lg shadow-sm">
                                    <span className="text-gray-700">İstemiyorum</span>
                                    <button
                                        onClick={handleNoFeature}
                                        className={`p-2 rounded-md transition-colors ${
                                            noFeatureSelected
                                                ? 'bg-orange-600 text-white'
                                                : firm.visible
                                                    ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        }`}
                                        disabled={!firm.visible}
                                    >
                                        {noFeatureSelected ? <FaMinus /> : <FaPlus />}
                                    </button>
                                </li>
                            </ul>
                        </div>
                    )}

                    {/* Aksiyon Butonları */}
                    <div className="flex flex-col gap-4">
                        <div className="grid grid-cols-2 gap-4">
                            {/* Sepete Ekle */}
                            <button
                                onClick={handleAddToCart}
                                className={`col-span-2 py-3 px-6 rounded-lg font-bold text-lg transition-colors flex items-center justify-center gap-2 ${
                                    firm.visible
                                        ? 'bg-orange-600 text-white hover:bg-orange-700'
                                        : 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                }`}
                                disabled={!firm.visible}
                            >
                                <FaPlus className="text-sm" />
                                Sepete Ekle
                            </button>

                            {/* Destek Al */}
                            <button
                                onClick={handleCallSupport}
                                className="py-3 px-6 rounded-lg font-bold bg-green-600 text-white hover:bg-green-700
                                         transition-colors flex items-center justify-center gap-2"
                            >
                                <FaPhoneAlt className="text-sm" />
                                Destek Al
                            </button>

                            {/* Paylaş */}
                            <button
                                onClick={handleShare}
                                className="py-3 px-6 rounded-lg font-bold bg-blue-600 text-white hover:bg-blue-700
                                         transition-colors flex items-center justify-center gap-2"
                            >
                                <FaShare className="text-sm" />
                                Paylaş
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Benzer Ürünler */}
            {similarProducts.length > 0 && (
                <div className="mt-16">
                    <h2 className="text-2xl font-bold mb-6">Benzer Ürünler</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        {similarProducts.map(similarProduct => (
                            <Link
                                key={similarProduct.id}
                                href={`/products/${similarProduct.id}`}
                                className="block group"
                            >
                                <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                                    <div className="relative pt-[100%]">
                                        <Image
                                            src={similarProduct.images[0]}
                                            alt={similarProduct.name}
                                            layout="fill"
                                            objectFit="cover"
                                            className="group-hover:scale-105 transition-transform duration-300"
                                        />
                                    </div>
                                    <div className="p-4">
                                        <h3 className="font-semibold text-gray-900 group-hover:text-orange-600
                                                     transition-colors line-clamp-2">
                                            {similarProduct.name}
                                        </h3>
                                        <p className="mt-2 text-orange-600 font-medium">
                                            {formatPrice(similarProduct.price).toFixed(2)} ₺
                                        </p>
                                    </div>
                                </div>
                            </Link>
                        ))}
                    </div>
                </div>
            )}

            {/* Bildirim Mesajı */}
            {message && (
                <div className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 
                                ${message.includes('kapalı')
                    ? 'bg-red-500'
                    : message.includes('seçin')
                        ? 'bg-yellow-500'
                        : 'bg-green-500'
                } text-white`}>
                    <div className="flex items-center gap-2">
                        {message.includes('kapalı') ? (
                            <FaExclamationCircle />
                        ) : message.includes('seçin') ? (
                            <FaExclamationCircle />
                        ) : (
                            <FaPlus />
                        )}
                        {message}
                    </div>
                </div>
            )}
        </div>
    );
}