'use client';
import Link from 'next/link';
import { FaInstagram, FaFacebook, FaGooglePlay, FaApple } from 'react-icons/fa';
import React from "react";

export default function Footer() {
    return (
        <footer className="bg-gradient-to-r from-orange-500 to-orange-600 text-white py-4 mt-10">
            <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
                {/* Sol taraf: Telif hakkı */}
                <div className="text-sm mb-4 md:mb-0">
                    © {new Date().getFullYear()} Yemek Kapımda
                </div>

                {/* Orta taraf: Sosyal medya ve diğer linkler */}
                <div className="flex space-x-4 mb-4 md:mb-0">
                    <Link href="/company/gizlilik" className="hover:text-orange-300">
                        Gizlilik Politikası
                    </Link>
                    <Link href="/company/mesafelisatis" className="hover:text-orange-300">
                        <PERSON><PERSON>li <PERSON>tı<PERSON> Sözleşmesi
                    </Link>
                    <Link href="/company/teslimatveiade" className="hover:text-orange-300">
                        Teslimat ve İade Şartları
                    </Link>
                    <Link href="/company/iletisim" className="hover:text-orange-300">
                        İletişim
                    </Link>
                </div>

                {/* Sağ taraf: Sosyal medya ikonları */}
                <div className="flex space-x-4 mb-4 md:mb-0">
                    <a href="https://www.instagram.com/yemekkapimda_42" target="_blank" rel="noopener noreferrer"
                       className="hover:text-orange-300" aria-label="Instagram">
                        <FaInstagram size={24}/>
                    </a>
                    <a href="https://www.facebook.com/profile.php?id=61565948951677" target="_blank"
                       rel="noopener noreferrer" className="hover:text-orange-300" aria-label="Facebook">
                        <FaFacebook size={24}/>
                    </a>
                </div>

                {/* Uygulama mağazası linkleri */}
                <div className="flex space-x-4 mb-4 md:mb-0">
                    <a 
                        href="https://play.google.com/store/apps/details?id=com.yemekkapimda.newapp" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                    >
                        <FaGooglePlay size={24} />
                        <div className="flex flex-col">
                            <span className="text-xs">HEMEN İNDİR</span>
                            <span className="text-sm font-medium">Google Play</span>
                        </div>
                    </a>
                    <a 
                        href="https://apps.apple.com/us/app/yemek-kap%C4%B1mda-seydi%C5%9Fehir/id6737899423" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                    >
                        <FaApple size={24} />
                        <div className="flex flex-col">
                            <span className="text-xs">HEMEN İNDİR</span>
                            <span className="text-sm font-medium">App Store</span>
                        </div>
                    </a>
                </div>

                {/* Alt taraf: Geliştirici bilgisi */}
                <div className="text-sm">
                    <a href="https://felixart.com.tr" target="_blank" rel="noopener noreferrer"
                       className="hover:text-orange-300" aria-label="Felixart">
                        felixart
                    </a>  ♥ ile geliştirdi. V: 2.0.1
                </div>
            </div>
        </footer>
    );
}