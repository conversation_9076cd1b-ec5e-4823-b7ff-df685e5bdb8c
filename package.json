{"name": "website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap"}, "dependencies": {"@react-google-maps/api": "^2.20.3", "dotenv": "^16.4.5", "firebase": "^10.13.1", "lucide-react": "^0.454.0", "next": "14.2.11", "next-sitemap": "^4.2.3", "react": "^18", "react-dom": "^18", "react-icons": "^5.3.0", "tailwind-scrollbar-hide": "^1.1.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.11", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}