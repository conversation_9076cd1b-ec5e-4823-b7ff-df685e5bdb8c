export interface Coupon {
    id: string;
    code: string;
    discountAmount: number;
    minOrderAmount: number;
    isActive: boolean;
    createdAt: any;
    updatedAt: any;
    usedBy?: string[]; // Kullanıcı ID'leri
    maxUsage?: number; // Maks<PERSON>um kullanım sayısı
    currentUsage?: number; // Mevcut kullanım sayısı
    expiryDate?: any; // Son kullanma tarihi
    description?: string;
    type: 'firstOrder' | 'general'; // Kupon tipi
}

export interface CouponValidationResult {
    isValid: boolean;
    message: string;
    discount: number;
    coupon?: Coupon;
}
